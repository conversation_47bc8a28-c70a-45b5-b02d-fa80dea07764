import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { FileText, Upload, Eye, Download, AlertCircle } from "lucide-react";
import { useState } from "react";

interface Document {
  id: string;
  name: string;
  category: "residency" | "bfaces" | "service";
  status: "verified" | "pending" | "rejected";
  uploadDate: string;
  fileSize: string;
  type: string;
}

// Dummy data
const dummyDocuments: Document[] = [
  {
    id: "1",
    name: "Proof of Residency - Electric Bill",
    category: "residency",
    status: "verified",
    uploadDate: "2024-04-01T10:30:00",
    fileSize: "2.4 MB",
    type: "PDF",
  },
  {
    id: "2",
    name: "BFACES - Income Statement",
    category: "bfaces",
    status: "pending",
    uploadDate: "2024-04-05T15:45:00",
    fileSize: "1.8 MB",
    type: "PDF",
  },
  {
    id: "3",
    name: "Medical Certificate",
    category: "service",
    status: "rejected",
    uploadDate: "2024-04-07T09:15:00",
    fileSize: "3.1 MB",
    type: "PDF",
  },
  {
    id: "4",
    name: "Valid ID - Front",
    category: "residency",
    status: "verified",
    uploadDate: "2024-04-02T11:20:00",
    fileSize: "1.2 MB",
    type: "JPG",
  },
];

export default function Documents() {
  const [activeTab, setActiveTab] = useState("all");

  const getStatusBadge = (status: Document["status"]) => {
    const variants = {
      verified: "default",
      pending: "secondary",
      rejected: "destructive",
    } as const;
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const filteredDocuments = activeTab === "all" 
    ? dummyDocuments 
    : dummyDocuments.filter(doc => doc.category === activeTab);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
  };

  return (
    <AppLayout>
      <Head title="Documents" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">My Documents</h1>
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Upload New Document
          </Button>
        </div>

        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Documents</TabsTrigger>
            <TabsTrigger value="residency">Residency</TabsTrigger>
            <TabsTrigger value="bfaces">BFACES</TabsTrigger>
            <TabsTrigger value="service">Service</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {filteredDocuments.map((doc) => (
              <Card key={doc.id} className="p-4">
                <div className="flex items-center gap-4">
                  <FileText className="h-8 w-8 text-gray-400" />
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{doc.name}</h3>
                        <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                          <span>Uploaded {formatDate(doc.uploadDate)}</span>
                          <span>•</span>
                          <span>{doc.fileSize}</span>
                          <span>•</span>
                          <span>{doc.type}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(doc.status)}
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    {doc.status === "rejected" && (
                      <div className="flex items-center gap-2 mt-2 text-sm text-red-600">
                        <AlertCircle className="h-4 w-4" />
                        <span>Document was rejected. Please upload a new version.</span>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}

            {filteredDocuments.length === 0 && (
              <Card className="p-8">
                <div className="text-center text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold">No documents found</h3>
                  <p>Upload your first document to get started.</p>
                </div>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="residency">
            {/* Content will be filtered automatically */}
          </TabsContent>
          <TabsContent value="bfaces">
            {/* Content will be filtered automatically */}
          </TabsContent>
          <TabsContent value="service">
            {/* Content will be filtered automatically */}
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
} 