import React from 'react';
import { Head, usePage, Link } from '@inertiajs/react';
import { type SharedData } from '@/types';
import LandingNav from '@/components/landing-nav';
import { PhoneIcon, MailIcon, FacebookIcon, TwitterIcon, InstagramIcon, AlertCircleIcon } from 'lucide-react';

interface LandingLayoutProps {
    children: React.ReactNode;
    title?: string;
}

export default function LandingLayout({ children, title }: LandingLayoutProps) {
    const { auth } = usePage<SharedData>().props;
    const currentPath = window.location.pathname;

    // Map the current path to route names
    let currentRoute = route('home');
    if (currentPath.includes('/services-info')) {
        currentRoute = route('services-info');
    } else if (currentPath.includes('/faqs')) {
        currentRoute = route('faqs');
    } else if (currentPath.includes('/contact')) {
        currentRoute = route('contact');
    }

    const pageTitle = title ? `${title} - Balagtas SocialCare` : 'Balagtas SocialCare';
    const pageDescription = title
        ? `${title} - Official social services platform for Balagtas residents. Access medical assistance, burial aid, senior citizen services, and more.`
        : 'Official social services platform for Balagtas residents. Streamlined access to medical assistance, burial aid, senior citizen services, and social welfare programs.';

    return (
        <>
            <Head title={pageTitle}>
                {/* Basic Meta Tags */}
                <meta name="description" content={pageDescription} />
                <meta name="keywords" content="Balagtas, social care, social services, medical assistance, burial assistance, senior citizens, BFACES, MSWDO, Bulacan, government services" />
                <meta name="author" content="Municipal Social Welfare and Development Office - Balagtas, Bulacan" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />

                {/* Open Graph Tags */}
                <meta property="og:title" content={pageTitle} />
                <meta property="og:description" content={pageDescription} />
                <meta property="og:type" content="website" />
                <meta property="og:site_name" content="Balagtas SocialCare" />
                <meta property="og:locale" content="en_PH" />

                {/* Twitter Card Tags */}
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={pageTitle} />
                <meta name="twitter:description" content={pageDescription} />

                {/* Government/Organization Schema */}
                <script type="application/ld+json">
                    {JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "GovernmentOrganization",
                        "name": "Municipal Social Welfare and Development Office - Balagtas",
                        "alternateName": "MSWDO Balagtas",
                        "description": "Official social services platform for Balagtas residents",
                        "address": {
                            "@type": "PostalAddress",
                            "streetAddress": "2nd Floor, Balagtas Municipal Hall",
                            "addressLocality": "Balagtas",
                            "addressRegion": "Bulacan",
                            "addressCountry": "Philippines"
                        },
                        "telephone": "(*************",
                        "email": "<EMAIL>",
                        "url": typeof window !== 'undefined' ? window.location.origin : '',
                        "sameAs": [
                            "https://www.facebook.com/balagtas.gov.ph"
                        ]
                    })}
                </script>

                {/* Fonts */}
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="min-h-screen bg-gradient-to-b from-white to-purple-50 flex flex-col">
                {/* Top Header Bar - Desktop */}
                <div className="bg-black text-white py-2 px-4 hidden md:block">
                    <div className="container mx-auto">
                        <div className="grid grid-cols-3 items-center">
                            {/* Left Column - Contact Info */}
                            <div className="flex items-center gap-6 text-sm">
                                <div className="flex items-center gap-2">
                                    <PhoneIcon className="h-4 w-4" />
                                    <span>(*************</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <MailIcon className="h-4 w-4" />
                                    <span><EMAIL></span>
                                </div>
                            </div>

                            {/* Center Column - Social Media */}
                            <div className="flex justify-center items-center gap-4">
                                <a
                                    href="#"
                                    className="text-white hover:text-purple-300 transition-colors"
                                    aria-label="Facebook"
                                >
                                    <FacebookIcon className="h-4 w-4" />
                                </a>
                                <a
                                    href="#"
                                    className="text-white hover:text-purple-300 transition-colors"
                                    aria-label="Twitter"
                                >
                                    <TwitterIcon className="h-4 w-4" />
                                </a>
                                <a
                                    href="#"
                                    className="text-white hover:text-purple-300 transition-colors"
                                    aria-label="Instagram"
                                >
                                    <InstagramIcon className="h-4 w-4" />
                                </a>
                            </div>

                            {/* Right Column - Emergency CTA */}
                            <div className="flex justify-end">
                                <a
                                    href="tel:(044)765-4321"
                                    className="flex items-center gap-2 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm font-medium transition-colors"
                                >
                                    <AlertCircleIcon className="h-4 w-4" />
                                    Emergency Hotline
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Top Header Bar - Mobile */}
                <div className="bg-black text-white py-2 px-4 md:hidden">
                    <div className="flex justify-between items-center text-sm">
                        <div className="flex items-center gap-2">
                            <PhoneIcon className="h-3 w-3" />
                            <span>(*************</span>
                        </div>
                        <a
                            href="tel:(044)765-4321"
                            className="flex items-center gap-1 bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs font-medium transition-colors"
                        >
                            <AlertCircleIcon className="h-3 w-3" />
                            Emergency
                        </a>
                    </div>
                </div>

                <LandingNav currentPath={currentRoute} auth={auth} />
                
                {/* Page Header */}
                {title && (
                    <div className="bg-purple-700 text-white py-12">
                        <div className="container mx-auto px-4">
                            <h1 className="text-3xl font-bold">{title}</h1>
                        </div>
                    </div>
                )}

                {/* Main Content */}
                <main className="flex-grow">
                    {children}
                </main>

                {/* Footer */}
                <footer className="bg-gray-900 text-gray-300">
                    <div className="container mx-auto px-4 py-12">
                        <div className="grid md:grid-cols-4 gap-8">
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Balagtas SocialCare</h3>
                                <p className="text-sm mb-3">
                                    The official social services platform for the residents of Balagtas.
                                </p>
                                <p className="text-sm text-purple-300">
                                    Managed and operated by the Municipal Social Welfare and Development Office (MSWDO) of Balagtas, Bulacan
                                </p>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Quick Links</h3>
                                <ul className="space-y-2 text-sm">
                                    <li><Link href={route('home')} className="hover:text-white">Home</Link></li>
                                    <li><Link href={route('services-info')} className="hover:text-white">Services</Link></li>
                                    <li><Link href={route('faqs')} className="hover:text-white">FAQs</Link></li>
                                    <li><Link href={route('contact')} className="hover:text-white">Contact</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Contact</h3>
                                <ul className="space-y-2 text-sm">
                                    <li>MSWDO - Municipal Hall</li>
                                    <li>Balagtas, Bulacan</li>
                                    <li>Phone: (*************</li>
                                    <li>Email: <EMAIL></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-white text-lg font-semibold mb-4">Office Hours</h3>
                                <ul className="space-y-2 text-sm">
                                    <li>Monday - Friday: 8:00 AM - 5:00 PM</li>
                                    <li>Saturday: 8:00 AM - 12:00 PM</li>
                                    <li>Sunday: Closed</li>
                                </ul>
                            </div>
                        </div>
                        <div className="border-t border-gray-800 mt-12 pt-8 text-sm text-center">
                            <p className="mb-2">© {new Date().getFullYear()} Balagtas SocialCare. All rights reserved.</p>
                            <p className="text-gray-400">A digital initiative by MSWDO Balagtas to streamline social welfare services for our community.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
} 