import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Download, Filter, Calendar, MapPin, Users, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Administration',
        href: '#',
    },
    {
        title: 'Reports and Analytics',
        href: '/mswdo-officer/analytics',
    },
];

export default function ReportsAndAnalytics() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reports and Analytics" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Reports and Analytics</h1>
                        <p className="text-purple-600 mt-2">Comprehensive reporting and data analytics dashboard</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Download className="h-4 w-4 mr-2" />
                            Export All
                        </Button>
                    </div>
                </div>

                {/* Overview Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Applications</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">3,456</div>
                            <p className="text-xs text-purple-600">All time</p>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Applications</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">234</div>
                            <p className="text-xs text-orange-600">Awaiting review</p>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Approved Applications</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">2,987</div>
                            <p className="text-xs text-green-600">Successfully processed</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Completion Rate</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">86%</div>
                            <p className="text-xs text-blue-600">Processing efficiency</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Interactive Graphs Section */}
                <div className="grid gap-6 lg:grid-cols-2">
                    {/* Services Processed Per Barangay */}
                    <Card className="border-purple-200">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-purple-900 flex items-center">
                                        <BarChart className="h-5 w-5 mr-2" />
                                        Services Processed Per Barangay
                                    </CardTitle>
                                    <CardDescription className="text-purple-600">
                                        Total processed services by barangay
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <select className="text-sm border border-purple-200 rounded px-2 py-1 focus:border-purple-400">
                                        <option value="day">Day</option>
                                        <option value="week">Week</option>
                                        <option value="month" selected>Month</option>
                                        <option value="year">Year</option>
                                    </select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-16 text-purple-600">
                                <BarChart className="h-16 w-16 mx-auto mb-4 text-purple-300" />
                                <h3 className="text-lg font-semibold mb-2">Services Per Barangay Chart</h3>
                                <p className="text-sm mb-4">Interactive bar chart showing service distribution across all 28 barangays</p>
                                <div className="text-xs text-purple-500 space-y-1">
                                    <p>• Click bars for detailed breakdown</p>
                                    <p>• Filter by time period</p>
                                    <p>• Export chart data</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Monthly Services Breakdown */}
                    <Card className="border-blue-200">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-blue-900 flex items-center">
                                        <BarChart className="h-5 w-5 mr-2" />
                                        Monthly Services Breakdown
                                    </CardTitle>
                                    <CardDescription className="text-blue-600">
                                        Annual view with service type breakdown
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <select className="text-sm border border-blue-200 rounded px-2 py-1 focus:border-blue-400">
                                        <option value="2023">2023</option>
                                        <option value="2024" selected>2024</option>
                                        <option value="2025">2025</option>
                                    </select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-16 text-blue-600">
                                <BarChart className="h-16 w-16 mx-auto mb-4 text-blue-300" />
                                <h3 className="text-lg font-semibold mb-2">Monthly Breakdown Chart</h3>
                                <p className="text-sm mb-4">Multi-bar chart with 3 bars per month</p>
                                <div className="text-xs text-blue-500 space-y-1">
                                    <div className="flex items-center justify-center gap-4">
                                        <div className="flex items-center gap-1">
                                            <div className="w-3 h-3 bg-purple-400 rounded"></div>
                                            <span>ID Applications</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <div className="w-3 h-3 bg-green-400 rounded"></div>
                                            <span>Assistance Applications</span>
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <div className="w-3 h-3 bg-orange-400 rounded"></div>
                                            <span>Certificate Applications</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>


            </div>
        </AppLayout>
    );
}
