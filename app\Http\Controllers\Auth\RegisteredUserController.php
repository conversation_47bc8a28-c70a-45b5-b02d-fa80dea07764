<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $validationRules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'address' => 'nullable|string|max:255',
            'barangay' => 'nullable|string|max:100',
            'contact_number' => 'nullable|string|max:20',
        ];

        $request->validate($validationRules);

        // All registrations are for Head of Family only
        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'applicant',
            'status' => 'unverified',
            'family_role' => 'head_of_family',
            'phone' => $request->contact_number,
            'document_verification_status' => 'pending',
            'bfaces_status' => 'documents_pending',
        ];

        $user = User::create($userData);

        // Create profile if additional data is provided
        if ($request->address || $request->barangay) {
            $user->profile()->create([
                'first_name' => explode(' ', $request->name)[0] ?? '',
                'last_name' => explode(' ', $request->name)[1] ?? '',
                'street_address' => $request->address,
                'barangay' => $request->barangay,
                'contact_number' => $request->contact_number,
            ]);
        }

        event(new Registered($user));

        Auth::login($user);

        // Redirect to dashboard with appropriate message
        return to_route('applicant.dashboard')->with('message',
            'Registration successful! Please upload your required documents to begin the BFACES process.'
        );
    }
}
