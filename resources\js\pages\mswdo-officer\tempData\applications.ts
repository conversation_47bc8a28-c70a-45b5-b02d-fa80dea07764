export const stats = {
    totalApplications: 2456,
    statusDistribution: {
        pending: 124,
        inProgress: 342,
        completed: 1890,
        rejected: 100
    },
    monthlyTrends: [
        { month: 'Jan', count: 180 },
        { month: 'Feb', count: 210 },
        { month: 'Mar', count: 245 },
        { month: 'Apr', count: 198 },
        { month: 'May', count: 267 },
        { month: 'Jun', count: 289 }
    ],
    processingMetrics: {
        averageProcessingTime: 5.2, // days
        completionRate: 88, // percentage
        satisfactionScore: 4.2 // out of 5
    },
    topLocations: [
        {
            city: "Manila",
            applications: 856,
            successRate: 92
        },
        {
            city: "Quezon City",
            applications: 634,
            successRate: 88
        },
        {
            city: "Caloocan",
            applications: 445,
            successRate: 76
        }
    ],
    demographicData: {
        ageGroups: {
            "18-25": 456,
            "26-35": 789,
            "36-45": 567,
            "46-55": 432,
            "56+": 212
        },
        gender: {
            male: 1123,
            female: 1298,
            other: 35
        },
        income: {
            low: 1234,
            medium: 987,
            high: 235
        }
    }
}; 