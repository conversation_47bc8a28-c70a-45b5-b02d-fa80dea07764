import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import { availablePermissions } from "@/pages/mswdo-officer/tempData/users";
import { router } from "@inertiajs/core";
import { ArrowLeft } from "lucide-react";

export default function NewAdmin() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    permissions: [] as string[]
  });

  const handlePermissionChange = (permission: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement form submission
    console.log("Form submitted:", formData);
    router.visit("/mswdo-officer/users");
  };

  const breadcrumbs = [
    {
      title: "User Management",
      href: "/mswdo-officer/users",
    },
    {
      title: "New Social Worker",
      href: "/mswdo-officer/users/social-workers/new",
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="New Admin" />
      
      <div className="p-6 space-y-6 max-w-[800px] mx-auto">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => router.visit("/superadmin/users")}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Users
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>New Admin</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter admin's full name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="Enter admin's email"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="Enter admin's phone number"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availablePermissions.map((permission) => (
                  <div key={permission.name} className="flex items-start space-x-2">
                    <Checkbox
                      id={permission.name}
                      checked={formData.permissions.includes(permission.name)}
                      onCheckedChange={() => handlePermissionChange(permission.name)}
                    />
                    <div className="grid gap-1.5 leading-none">
                      <label
                        htmlFor={permission.name}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {permission.module}
                      </label>
                      <p className="text-sm text-muted-foreground">
                        {permission.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.visit("/superadmin/users")}
            >
              Cancel
            </Button>
            <Button type="submit">Create Admin</Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
} 