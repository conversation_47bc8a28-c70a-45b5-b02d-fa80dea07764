import { Head, useForm } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Service {
  id: number
  name: string
  description: string
  slug: string
  is_active: boolean
}

interface ServiceRequirement {
  id: number
  service_id: number
  name: string
  description: string | null
  document_type: string
  max_file_size: number
  validation_rules: string[]
  validity_period: number | null
  order: number
  is_required: boolean
  is_active: boolean
}

interface Props {
  services: Service[]
  requirement?: ServiceRequirement
}

export default function ServiceRequirementForm({ services, requirement }: Props) {
  const isEditing = !!requirement
  const { data, setData, post, put, processing, errors } = useForm({
    service_id: requirement?.service_id ?? "",
    name: requirement?.name ?? "",
    description: requirement?.description ?? "",
    document_type: requirement?.document_type ?? "pdf",
    max_file_size: requirement?.max_file_size ?? 5,
    validation_rules: requirement?.validation_rules ?? [],
    validity_period: requirement?.validity_period ?? "",
    order: requirement?.order ?? 1,
    is_required: requirement?.is_required ?? true,
    is_active: requirement?.is_active ?? true,
  })

  const documentTypes = [
    { value: "pdf", label: "PDF Document" },
    { value: "image", label: "Image (JPG, PNG)" },
    { value: "doc", label: "Word Document" },
    { value: "any", label: "Any File Type" },
  ]

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/superadmin/dashboard" },
    { title: "Services Configuration", href: "/superadmin/services" },
    { 
      title: isEditing ? "Edit Requirement" : "New Requirement", 
      href: isEditing ? `/superadmin/services/requirements/${requirement.id}` : "/superadmin/services/requirements/new"
    },
  ]

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    if (isEditing) {
      put(`/superadmin/services/requirements/${requirement.id}`)
    } else {
      post("/superadmin/services/requirements")
    }
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={isEditing ? "Edit Requirement" : "New Requirement"} />

      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <Heading 
            title={isEditing ? "Edit Requirement" : "New Requirement"}
            description={
              isEditing 
                ? "Update an existing service requirement."
                : "Create a new service requirement."
            }
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                General information about the requirement.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="service_id">Service</Label>
                <select
                  id="service_id"
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={data.service_id}
                  onChange={e => setData("service_id", e.target.value)}
                >
                  <option value="">Select a service</option>
                  {services.map(service => (
                    <option key={service.id} value={service.id}>
                      {service.name}
                    </option>
                  ))}
                </select>
                {errors.service_id && (
                  <p className="text-sm text-red-500">{errors.service_id}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={data.name}
                  onChange={e => setData("name", e.target.value)}
                  placeholder="Enter requirement name"
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={e => setData("description", e.target.value)}
                  placeholder="Enter requirement description"
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Document Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Document Settings</CardTitle>
              <CardDescription>
                Configure document type and validation rules.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="document_type">Document Type</Label>
                  <Select
                    value={data.document_type}
                    onValueChange={value => setData("document_type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      {documentTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.document_type && (
                    <p className="text-sm text-red-500">{errors.document_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_file_size">Maximum File Size (MB)</Label>
                  <Input
                    id="max_file_size"
                    type="number"
                    min="1"
                    value={data.max_file_size}
                    onChange={e => setData("max_file_size", Number(e.target.value))}
                  />
                  {errors.max_file_size && (
                    <p className="text-sm text-red-500">{errors.max_file_size}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="validity_period">Validity Period (days)</Label>
                <Input
                  id="validity_period"
                  type="number"
                  value={data.validity_period}
                  onChange={e => setData("validity_period", e.target.value)}
                  placeholder="Optional - leave empty for no expiry"
                />
                {errors.validity_period && (
                  <p className="text-sm text-red-500">{errors.validity_period}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="order">Display Order</Label>
                <Input
                  id="order"
                  type="number"
                  min="1"
                  value={data.order}
                  onChange={e => setData("order", Number(e.target.value))}
                />
                {errors.order && (
                  <p className="text-sm text-red-500">{errors.order}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>
                Additional requirement configuration.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_required"
                  checked={data.is_required}
                  onCheckedChange={checked => setData("is_required", checked)}
                />
                <Label htmlFor="is_required">Required</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={data.is_active}
                  onCheckedChange={checked => setData("is_active", checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" asChild>
              <a href="/superadmin/services">Cancel</a>
            </Button>
            <Button type="submit" disabled={processing}>
              {isEditing ? "Update Requirement" : "Create Requirement"}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  )
} 