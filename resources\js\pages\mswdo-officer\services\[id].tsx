import { Head, useForm } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useState } from "react"

interface Category {
  id: number
  name: string
  description: string | null
  slug: string
  icon: string | null
  is_active: boolean
}

interface Service {
  id: number
  category_id: number
  name: string
  description: string
  slug: string
  max_amount: number | null
  requirements: string[]
  schedule: {
    days: string[]
    hours: string
  }
  requires_verification: boolean
  is_active: boolean
}

interface Props {
  categories: Category[]
  service?: Service
}

export default function ServiceForm({ categories, service }: Props) {
  const isEditing = !!service
  const { data, setData, post, put, processing, errors } = useForm({
    category_id: service?.category_id ?? "",
    name: service?.name ?? "",
    description: service?.description ?? "",
    max_amount: service?.max_amount ?? "",
    requirements: service?.requirements ?? [],
    schedule: service?.schedule ?? {
      days: [],
      hours: "9:00 AM - 5:00 PM"
    },
    requires_verification: service?.requires_verification ?? true,
    is_active: service?.is_active ?? true,
  })

  const [newRequirement, setNewRequirement] = useState("")
  const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/superadmin/dashboard" },
    { title: "Services Configuration", href: "/superadmin/services" },
    { 
      title: isEditing ? "Edit Service" : "New Service", 
      href: isEditing ? `/superadmin/services/${service.id}` : "/superadmin/services/new"
    },
  ]

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    if (isEditing) {
      put(`/superadmin/services/${service.id}`)
    } else {
      post("/superadmin/services")
    }
  }

  function addRequirement(e: React.FormEvent) {
    e.preventDefault()
    if (newRequirement.trim()) {
      setData("requirements", [...data.requirements, newRequirement.trim()])
      setNewRequirement("")
    }
  }

  function removeRequirement(index: number) {
    setData("requirements", data.requirements.filter((_, i) => i !== index))
  }

  function toggleDay(day: string) {
    const days = data.schedule.days.includes(day)
      ? data.schedule.days.filter(d => d !== day)
      : [...data.schedule.days, day]
    setData("schedule", { ...data.schedule, days })
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={isEditing ? "Edit Service" : "New Service"} />

      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <Heading 
            title={isEditing ? "Edit Service" : "New Service"}
            description={
              isEditing 
                ? "Update an existing service."
                : "Create a new service."
            }
          />
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                General information about the service.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="category_id">Category</Label>
                <select
                  id="category_id"
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  value={data.category_id}
                  onChange={e => setData("category_id", e.target.value)}
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.category_id && (
                  <p className="text-sm text-red-500">{errors.category_id}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={data.name}
                  onChange={e => setData("name", e.target.value)}
                  placeholder="Enter service name"
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={e => setData("description", e.target.value)}
                  placeholder="Enter service description"
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Requirements Section */}
          <Card>
            <CardHeader>
              <CardTitle>Requirements</CardTitle>
              <CardDescription>
                Define the requirements needed to avail this service.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={newRequirement}
                    onChange={e => setNewRequirement(e.target.value)}
                    placeholder="Add a requirement"
                  />
                  <Button type="button" onClick={addRequirement}>
                    Add
                  </Button>
                </div>

                <div className="space-y-2">
                  {data.requirements.map((requirement, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <span>{requirement}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeRequirement(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  {data.requirements.length === 0 && (
                    <p className="text-sm text-muted-foreground">No requirements added yet.</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="requires_verification"
                    checked={data.requires_verification}
                    onCheckedChange={checked => setData("requires_verification", checked)}
                  />
                  <Label htmlFor="requires_verification">Requires Verification</Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Enable if this service requires verification before processing.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Schedule */}
          <Card>
            <CardHeader>
              <CardTitle>Schedule</CardTitle>
              <CardDescription>
                Set service availability schedule.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Available Days</Label>
                <div className="flex flex-wrap gap-2">
                  {weekDays.map(day => (
                    <Button
                      key={day}
                      type="button"
                      variant={data.schedule.days.includes(day) ? "default" : "outline"}
                      onClick={() => toggleDay(day)}
                    >
                      {day}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedule_hours">Hours</Label>
                <Input
                  id="schedule_hours"
                  value={data.schedule.hours}
                  onChange={e => setData("schedule", { ...data.schedule, hours: e.target.value })}
                  placeholder="e.g., 9:00 AM - 5:00 PM"
                />
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>
                Additional service configuration.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={data.is_active}
                  onCheckedChange={checked => setData("is_active", checked)}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" type="button" asChild>
              <a href="/superadmin/services">Cancel</a>
            </Button>
            <Button type="submit" disabled={processing}>
              {isEditing ? "Update Service" : "Create Service"}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  )
} 