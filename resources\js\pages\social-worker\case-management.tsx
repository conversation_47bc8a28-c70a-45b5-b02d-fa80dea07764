import { Head } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertCircle, Calendar, FileText, Filter, Search, User2 } from "lucide-react"
import { useState } from "react"

interface Case {
  id: number
  client: {
    id: number
    name: string
    email: string
    phone?: string
    barangay?: string
    address?: string
  }
  type: "bfaces" | "service" | "general"
  status: "new" | "in_progress" | "pending_review" | "resolved" | "closed"
  priority: "low" | "medium" | "high" | "urgent"
  assignedAt: string
  lastUpdated: string
  nextFollowUp?: string
  description: string
  notes?: string[]
}

interface Props {
  cases: Case[]
  totalCases: number
  resolvedCases: number
  pendingCases: number
  urgentCases: number
}

export default function CaseManagement({ 
  cases, 
  totalCases, 
  resolvedCases, 
  pendingCases,
  urgentCases,
}: Props) {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<Case["status"] | "all">("all")
  const [typeFilter, setTypeFilter] = useState<Case["type"] | "all">("all")
  const [priorityFilter, setPriorityFilter] = useState<Case["priority"] | "all">("all")

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "Case Management", href: "/admin/case-management" },
  ]

  function getStatusColor(status: Case["status"]) {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800"
      case "in_progress":
        return "bg-yellow-100 text-yellow-800"
      case "pending_review":
        return "bg-purple-100 text-purple-800"
      case "resolved":
        return "bg-green-100 text-green-800"
      case "closed":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  function getPriorityColor(priority: Case["priority"]) {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredCases = cases.filter(case_ => {
    const matchesSearch = case_.client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      case_.id.toString().includes(searchQuery)
    
    const matchesStatus = statusFilter === "all" || case_.status === statusFilter
    const matchesType = typeFilter === "all" || case_.type === typeFilter
    const matchesPriority = priorityFilter === "all" || case_.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Case Management" />

      <div className="flex flex-col gap-6">
        <Heading 
          title="Case Management" 
          description="View and manage your assigned cases." 
        />

        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Cases
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCases}</div>
              <p className="text-xs text-muted-foreground">
                Assigned to you
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Resolved Cases
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{resolvedCases}</div>
              <p className="text-xs text-muted-foreground">
                {((resolvedCases / totalCases) * 100).toFixed(1)}% resolution rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Pending Cases
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingCases}</div>
              <p className="text-xs text-muted-foreground">
                Requiring attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Urgent Cases
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{urgentCases}</div>
              <p className="text-xs text-muted-foreground">
                High priority cases
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by client name or case ID..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-2">
                <div className="flex items-center gap-1.5">
                  <Label htmlFor="status" className="text-sm">
                    Status
                  </Label>
                  <select
                    id="status"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value as Case["status"] | "all")}
                  >
                    <option value="all">All</option>
                    <option value="new">New</option>
                    <option value="in_progress">In Progress</option>
                    <option value="pending_review">Pending Review</option>
                    <option value="resolved">Resolved</option>
                    <option value="closed">Closed</option>
                  </select>
                </div>

                <div className="flex items-center gap-1.5">
                  <Label htmlFor="type" className="text-sm">
                    Type
                  </Label>
                  <select
                    id="type"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={typeFilter}
                    onChange={e => setTypeFilter(e.target.value as Case["type"] | "all")}
                  >
                    <option value="all">All</option>
                    <option value="bfaces">BFACES</option>
                    <option value="service">Service</option>
                    <option value="general">General</option>
                  </select>
                </div>

                <div className="flex items-center gap-1.5">
                  <Label htmlFor="priority" className="text-sm">
                    Priority
                  </Label>
                  <select
                    id="priority"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={priorityFilter}
                    onChange={e => setPriorityFilter(e.target.value as Case["priority"] | "all")}
                  >
                    <option value="all">All</option>
                    <option value="urgent">Urgent</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>

                <Button variant="outline" size="sm" onClick={() => {
                  setSearchQuery("")
                  setStatusFilter("all")
                  setTypeFilter("all")
                  setPriorityFilter("all")
                }}>
                  <Filter className="mr-2 h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cases List */}
        <div className="space-y-4">
          {filteredCases.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No Cases Found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Try adjusting your filters or search query.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredCases.map(case_ => (
              <Card key={case_.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>Case #{case_.id}</CardTitle>
                      <CardDescription className="mt-2">
                        {case_.description}
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getPriorityColor(case_.priority)}>
                        {case_.priority.charAt(0).toUpperCase() + case_.priority.slice(1)}
                      </Badge>
                      <Badge className={getStatusColor(case_.status)}>
                        {case_.status.split("_").map(word => 
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(" ")}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User2 className="mr-2 h-4 w-4" />
                        Client: {case_.client.name}
                      </div>
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        Type: {case_.type.toUpperCase()}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        Last Updated: {new Date(case_.lastUpdated).toLocaleDateString()}
                      </div>
                      {case_.nextFollowUp && (
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4" />
                          Next Follow-up: {new Date(case_.nextFollowUp).toLocaleDateString()}
                        </div>
                      )}
                    </div>

                    {case_.notes && case_.notes.length > 0 && (
                      <div className="rounded-md bg-muted p-4">
                        <h4 className="mb-2 text-sm font-medium">Recent Notes</h4>
                        <div className="space-y-2">
                          {case_.notes.slice(0, 2).map((note, index) => (
                            <p key={index} className="text-sm text-muted-foreground">
                              {note}
                            </p>
                          ))}
                          {case_.notes.length > 2 && (
                            <Button variant="link" className="h-auto p-0 text-xs">
                              View all {case_.notes.length} notes
                            </Button>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" asChild>
                        <a href={`/admin/case-management/${case_.id}`}>
                          View Details
                        </a>
                      </Button>
                      <Button asChild>
                        <a href={`/admin/case-management/${case_.id}/update`}>
                          Update Case
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </AppLayout>
  )
} 