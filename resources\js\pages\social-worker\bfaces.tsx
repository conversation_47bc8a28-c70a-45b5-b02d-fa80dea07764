import { Head } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, Calendar, FileText, Filter, Loader2, Search, User2 } from "lucide-react"
import { useState } from "react"
import { useForm } from "@inertiajs/react"

interface BFACESApplication {
  id: number
  client: {
    id: number
    name: string
    email: string
    phone?: string
    barangay?: string
    address?: string
  }
  status: "submitted" | "under_review" | "approved" | "rejected"
  submittedAt: string
  reviewedAt?: string
  reviewedBy?: {
    id: number
    name: string
  }
  rejectionReason?: string
  crisisType: string
  crisisDescription: string
  assistanceNeeded: string
  monthlyIncome: number
  familyMembers: {
    name: string
    age: string
    relationship: string
    occupation: string
  }[]
  documents: {
    id: number
    name: string
    type: string
    url: string
    verified: boolean
  }[]
  notes?: string[]
}

interface Props {
  applications: BFACESApplication[]
  totalSubmitted: number
  totalUnderReview: number
  totalApproved: number
  totalRejected: number
}

export default function BFACESApplications({ 
  applications, 
  totalSubmitted,
  totalUnderReview,
  totalApproved,
  totalRejected,
}: Props) {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<BFACESApplication["status"] | "all">("all")

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "BFACES Applications", href: "/admin/bfaces" },
  ]

  const { data, setData, post, processing, errors } = useForm({
    note: "",
    rejectionReason: "",
  })

  function getStatusColor(status: BFACESApplication["status"]) {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800"
      case "under_review":
        return "bg-yellow-100 text-yellow-800"
      case "approved":
        return "bg-green-100 text-green-800"
      case "rejected":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.id.toString().includes(searchQuery)
    
    const matchesStatus = statusFilter === "all" || app.status === statusFilter

    return matchesSearch && matchesStatus
  })

  function startReview(id: number) {
    post(`/social-worker/bfaces/${id}/review`)
  }

  function approveApplication(id: number) {
    post(`/admin/bfaces/${id}/approve`)
  }

  function rejectApplication(id: number) {
    if (!data.rejectionReason) return
    
    post(`/admin/bfaces/${id}/reject`, {
      onSuccess: () => {
        setData("rejectionReason", "")
      },
    })
  }

  function addNote(id: number) {
    if (!data.note) return

    post(`/admin/bfaces/${id}/notes`, {
      onSuccess: () => {
        setData("note", "")
      },
    })
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="BFACES Applications" />

      <div className="flex flex-col gap-6">
        <Heading 
          title="BFACES Applications" 
          description="Review and process emergency assistance applications." 
        />

        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                New Applications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalSubmitted}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Under Review
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUnderReview}</div>
              <p className="text-xs text-muted-foreground">
                Being processed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Approved
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalApproved}</div>
              <p className="text-xs text-muted-foreground">
                Ready for assistance
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Rejected
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalRejected}</div>
              <p className="text-xs text-muted-foreground">
                Did not qualify
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by client name or application ID..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-2">
                <div className="flex items-center gap-1.5">
                  <Label htmlFor="status" className="text-sm">
                    Status
                  </Label>
                  <select
                    id="status"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value as BFACESApplication["status"] | "all")}
                  >
                    <option value="all">All</option>
                    <option value="submitted">Submitted</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>

                <Button variant="outline" size="sm" onClick={() => {
                  setSearchQuery("")
                  setStatusFilter("all")
                }}>
                  <Filter className="mr-2 h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Applications List */}
        <div className="space-y-4">
          {filteredApplications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No Applications Found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Try adjusting your filters or search query.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredApplications.map(application => (
              <Card key={application.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>Application #{application.id}</CardTitle>
                      <CardDescription className="mt-2">
                        Crisis Type: {application.crisisType}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusColor(application.status)}>
                      {application.status.split("_").map(word => 
                        word.charAt(0).toUpperCase() + word.slice(1)
                      ).join(" ")}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <User2 className="mr-2 h-4 w-4" />
                        Client: {application.client.name}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        Submitted: {new Date(application.submittedAt).toLocaleDateString()}
                      </div>
                      {application.reviewedAt && (
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4" />
                          Reviewed: {new Date(application.reviewedAt).toLocaleDateString()}
                          {application.reviewedBy && ` by ${application.reviewedBy.name}`}
                        </div>
                      )}
                    </div>

                    <div className="rounded-md bg-muted p-4">
                      <h4 className="mb-2 text-sm font-medium">Crisis Description</h4>
                      <p className="text-sm text-muted-foreground">
                        {application.crisisDescription}
                      </p>
                      <h4 className="mb-2 mt-4 text-sm font-medium">Assistance Needed</h4>
                      <p className="text-sm text-muted-foreground">
                        {application.assistanceNeeded}
                      </p>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="mb-2 text-sm font-medium">Family Information</h4>
                        <div className="space-y-2">
                          <p className="text-sm text-muted-foreground">
                            Monthly Income: ₱{application.monthlyIncome.toLocaleString()}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Family Members: {application.familyMembers.length}
                          </p>
                        </div>
                      </div>

                      <div>
                        <h4 className="mb-2 text-sm font-medium">Documents</h4>
                        <div className="space-y-2">
                          {application.documents.map(doc => (
                            <div key={doc.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm text-muted-foreground">
                                  {doc.name}
                                </span>
                              </div>
                              <Badge variant={doc.verified ? "default" : "secondary"}>
                                {doc.verified ? "Verified" : "Pending"}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {application.status === "rejected" && application.rejectionReason && (
                      <div className="rounded-md bg-red-50 p-4">
                        <p className="text-sm font-medium text-red-800">
                          Rejection Reason:
                        </p>
                        <p className="mt-1 text-sm text-red-700">
                          {application.rejectionReason}
                        </p>
                      </div>
                    )}

                    {application.notes && application.notes.length > 0 && (
                      <div className="rounded-md bg-muted p-4">
                        <h4 className="mb-2 text-sm font-medium">Notes</h4>
                        <div className="space-y-2">
                          {application.notes.map((note, index) => (
                            <p key={index} className="text-sm text-muted-foreground">
                              {note}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <Button variant="outline" asChild>
                        <a href={`/admin/bfaces/${application.id}`}>
                          View Full Details
                        </a>
                      </Button>

                      <div className="flex gap-2">
                        {application.status === "submitted" && (
                          <Button
                            onClick={() => startReview(application.id)}
                            disabled={processing}
                          >
                            {processing ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Starting Review...
                              </>
                            ) : (
                              "Start Review"
                            )}
                          </Button>
                        )}

                        {application.status === "under_review" && (
                          <>
                            <Button
                              variant="outline"
                              onClick={() => {
                                const reason = window.prompt("Enter rejection reason:")
                                if (reason) {
                                  setData("rejectionReason", reason)
                                  rejectApplication(application.id)
                                }
                              }}
                              disabled={processing}
                            >
                              {processing ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Rejecting...
                                </>
                              ) : (
                                "Reject"
                              )}
                            </Button>
                            <Button
                              onClick={() => approveApplication(application.id)}
                              disabled={processing}
                            >
                              {processing ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Approving...
                                </>
                              ) : (
                                "Approve"
                              )}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </AppLayout>
  )
} 