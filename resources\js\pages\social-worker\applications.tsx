import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, User, Calendar, FileText, AlertCircle, CheckCircle, XCircle, Clock } from "lucide-react";
import { useState, useCallback } from "react";
import { type BreadcrumbItem } from "@/types";
import { applications } from "./tempData";
import { type Application } from "./tempData/types";

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: "Admin",
    href: "/admin/dashboard",
  },
  {
    title: "Applications",
    href: "/admin/applications",
  },
];

export default function Applications() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [serviceFilter, setServiceFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  const filteredApplications = applications.filter(app => {
    const matchesStatus = statusFilter === "all" || app.status === statusFilter;
    const matchesService = serviceFilter === "all" || app.service === serviceFilter;
    const matchesPriority = priorityFilter === "all" || app.priority === priorityFilter;
    const matchesSearch = 
      app.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Filter based on active tab
    if (activeTab === "all") return matchesStatus && matchesService && matchesPriority && matchesSearch;
    return app.status === activeTab && matchesService && matchesPriority && matchesSearch;
  });

  const getStatusBadge = useCallback((status: Application["status"]) => {
    const variants: Record<Application["status"], "outline" | "default" | "destructive" | "secondary"> = {
      pending: "outline",
      in_progress: "default",
      approved: "secondary",
      rejected: "destructive"
    };
    const statusDisplay = status.replace('_', ' ');
    return <Badge variant={variants[status]}>{statusDisplay}</Badge>;
  }, []);

  const getPriorityBadge = useCallback((priority: Application["priority"]) => {
    const variants: Record<Application["priority"], "outline" | "default" | "destructive"> = {
      low: "outline",
      medium: "default",
      high: "destructive"
    };
    return <Badge variant={variants[priority]}>{priority}</Badge>;
  }, []);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Service Applications" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Service Applications</h1>
            <p className="text-sm text-gray-500 mt-1">
              Process and manage client service applications
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by client or ID..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="service">Service</Label>
              <Select value={serviceFilter} onValueChange={setServiceFilter}>
                <SelectTrigger id="service">
                  <SelectValue placeholder="Filter by service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Services</SelectItem>
                  <SelectItem value="Medical Assistance">Medical Assistance</SelectItem>
                  <SelectItem value="Burial Assistance">Burial Assistance</SelectItem>
                  <SelectItem value="Educational Aid">Educational Aid</SelectItem>
                  <SelectItem value="Senior Citizen Aid">Senior Citizen Aid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger id="priority">
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="all">All Applications</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="in_progress">In Progress</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          {filteredApplications.map((application) => (
            <Card key={application.id} className="p-4">
              <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="space-y-1">
                    <div className="flex flex-wrap gap-2 items-center">
                      <h3 className="font-semibold text-lg">Application #{application.id}</h3>
                      {getStatusBadge(application.status)}
                      {getPriorityBadge(application.priority)}
                    </div>
                    <p className="text-sm text-gray-500">{application.service}</p>
                  </div>
                </div>
                
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Client</div>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <a href={`/admin/clients/${application.clientId}`} className="text-sm text-blue-600 hover:underline">
                        {application.clientName}
                      </a>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Timeline</div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">Submitted: {application.submittedAt}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">Updated: {application.lastUpdated}</span>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <div className="text-sm font-medium">Assigned To</div>
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{application.assignedTo || 'Unassigned'}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {application.notes && (
                <div className="mt-3 pt-3 border-t">
                  <div className="text-sm text-gray-700">
                    <span className="font-medium">Notes: </span>
                    {application.notes}
                  </div>
                </div>
              )}
            </Card>
          ))}

          {filteredApplications.length === 0 && (
            <Card className="p-8">
              <div className="text-center text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No applications found</h3>
                <p>There are no applications matching your filters.</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
} 