<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BfacesForm extends Model
{
    protected $fillable = [
        'head_of_family_id',
        'bfaces_control_code',
        'household_composition',
        'total_monthly_income',
        'income_sources',
        'total_household_members',
        'housing_type',
        'housing_description',
        'has_electricity',
        'has_water_supply',
        'has_toilet_facility',
        'vulnerabilities',
        'current_assistance_received',
        'special_circumstances',
        'poverty_level',
        'per_capita_income',
        'priority_needs',
        'form_status',
        'assessed_by',
        'submitted_at',
        'assessed_at',
        'assessment_notes',
        'approved_by',
        'approved_at',
        'control_code_generated_at'
    ];

    protected $casts = [
        'household_composition' => 'array',
        'income_sources' => 'array',
        'vulnerabilities' => 'array',
        'current_assistance_received' => 'array',
        'priority_needs' => 'array',
        'total_monthly_income' => 'decimal:2',
        'per_capita_income' => 'decimal:2',
        'has_electricity' => 'boolean',
        'has_water_supply' => 'boolean',
        'has_toilet_facility' => 'boolean',
        'submitted_at' => 'datetime',
        'assessed_at' => 'datetime',
        'approved_at' => 'datetime',
        'control_code_generated_at' => 'datetime'
    ];

    public function headOfFamily(): BelongsTo
    {
        return $this->belongsTo(User::class, 'head_of_family_id');
    }

    public function assessor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assessed_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function isSubmitted(): bool
    {
        return $this->form_status === 'submitted';
    }

    public function isApproved(): bool
    {
        return $this->form_status === 'approved';
    }

    public function isDraft(): bool
    {
        return $this->form_status === 'draft';
    }

    public function calculatePerCapitaIncome(): float
    {
        if ($this->total_household_members <= 0 || !$this->total_monthly_income) {
            return 0;
        }

        return $this->total_monthly_income / $this->total_household_members;
    }

    public function updatePerCapitaIncome(): void
    {
        $this->update([
            'per_capita_income' => $this->calculatePerCapitaIncome()
        ]);
    }

    public function generateControlCode(): string
    {
        if ($this->bfaces_control_code) {
            return $this->bfaces_control_code;
        }

        do {
            $code = 'BFC' . str_pad(random_int(0, 999999999), 9, '0', STR_PAD_LEFT);
        } while (self::where('bfaces_control_code', $code)->exists());

        $this->update([
            'bfaces_control_code' => $code,
            'control_code_generated_at' => now()
        ]);

        // Also update the head of family's control code
        $this->headOfFamily->update(['bfaces_control_code' => $code]);

        return $code;
    }
}
