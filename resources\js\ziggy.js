const Ziggy = {"url":"http:\/\/balagtas-social-care.test","port":null,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"services-info":{"uri":"services-info","methods":["GET","HEAD"]},"faqs":{"uri":"faqs","methods":["GET","HEAD"]},"contact":{"uri":"contact","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"applicant.dashboard":{"uri":"applicant\/dashboard","methods":["GET","HEAD"]},"applicant.residency-verification":{"uri":"applicant\/residency-verification","methods":["GET","HEAD"]},"applicant.bfaces-application":{"uri":"applicant\/bfaces-application","methods":["GET","HEAD"]},"applicant.services":{"uri":"applicant\/services","methods":["GET","HEAD"]},"applicant.applications":{"uri":"applicant\/applications","methods":["GET","HEAD"]},"applicant.appointments":{"uri":"applicant\/appointments","methods":["GET","HEAD"]},"applicant.documents":{"uri":"applicant\/documents","methods":["GET","HEAD"]},"applicant.settings.profile":{"uri":"applicant\/settings\/profile","methods":["GET","HEAD"]},"applicant.settings.profile.update":{"uri":"applicant\/settings\/profile","methods":["PATCH"]},"applicant.settings.password":{"uri":"applicant\/settings\/password","methods":["GET","HEAD"]},"applicant.settings.password.update":{"uri":"applicant\/settings\/password","methods":["PUT"]},"social-worker.dashboard":{"uri":"social-worker\/dashboard","methods":["GET","HEAD"]},"social-worker.verifications":{"uri":"social-worker\/verifications","methods":["GET","HEAD"]},"social-worker.applications":{"uri":"social-worker\/applications","methods":["GET","HEAD"]},"social-worker.interviews":{"uri":"social-worker\/interviews","methods":["GET","HEAD"]},"social-worker.reports":{"uri":"social-worker\/reports","methods":["GET","HEAD"]},"social-worker.clients":{"uri":"social-worker\/clients","methods":["GET","HEAD"]},"social-worker.clients.show":{"uri":"social-worker\/clients\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"social-worker.settings.profile":{"uri":"social-worker\/settings\/profile","methods":["GET","HEAD"]},"social-worker.settings.profile.update":{"uri":"social-worker\/settings\/profile","methods":["PATCH"]},"social-worker.settings.password":{"uri":"social-worker\/settings\/password","methods":["GET","HEAD"]},"social-worker.settings.password.update":{"uri":"social-worker\/settings\/password","methods":["PUT"]},"social-worker.settings.appearance":{"uri":"social-worker\/settings\/appearance","methods":["GET","HEAD"]},"mswdo-officer.dashboard":{"uri":"mswdo-officer\/dashboard","methods":["GET","HEAD"]},"mswdo-officer.configuration":{"uri":"mswdo-officer\/configuration","methods":["GET","HEAD"]},"mswdo-officer.services":{"uri":"mswdo-officer\/services","methods":["GET","HEAD"]},"mswdo-officer.services.create":{"uri":"mswdo-officer\/services\/new","methods":["GET","HEAD"]},"mswdo-officer.services.edit":{"uri":"mswdo-officer\/services\/{id}\/edit","methods":["GET","HEAD"],"parameters":["id"]},"mswdo-officer.services.categories.create":{"uri":"mswdo-officer\/services\/categories\/new","methods":["GET","HEAD"]},"mswdo-officer.services.categories.edit":{"uri":"mswdo-officer\/services\/categories\/{id}\/edit","methods":["GET","HEAD"],"parameters":["id"]},"mswdo-officer.services.requirements.create":{"uri":"mswdo-officer\/services\/requirements\/new","methods":["GET","HEAD"]},"mswdo-officer.services.requirements.edit":{"uri":"mswdo-officer\/services\/requirements\/{id}\/edit","methods":["GET","HEAD"],"parameters":["id"]},"mswdo-officer.users":{"uri":"mswdo-officer\/users","methods":["GET","HEAD"]},"mswdo-officer.budget":{"uri":"mswdo-officer\/budget","methods":["GET","HEAD"]},"mswdo-officer.reports":{"uri":"mswdo-officer\/reports","methods":["GET","HEAD"]},"mswdo-officer.database":{"uri":"mswdo-officer\/database","methods":["GET","HEAD"]},"mswdo-officer.settings.profile":{"uri":"mswdo-officer\/settings\/profile","methods":["GET","HEAD"]},"mswdo-officer.settings.profile.update":{"uri":"mswdo-officer\/settings\/profile","methods":["PATCH"]},"mswdo-officer.settings.password":{"uri":"mswdo-officer\/settings\/password","methods":["GET","HEAD"]},"mswdo-officer.settings.password.update":{"uri":"mswdo-officer\/settings\/password","methods":["PUT"]},"mswdo-officer.settings.appearance":{"uri":"mswdo-officer\/settings\/appearance","methods":["GET","HEAD"]},"mswdo-officer.users.social-workers.create":{"uri":"mswdo-officer\/users\/social-workers\/new","methods":["GET","HEAD"]},"mswdo-officer.users.social-workers.edit":{"uri":"mswdo-officer\/users\/social-workers\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"mswdo-officer.users.applicants.create":{"uri":"mswdo-officer\/users\/applicants\/new","methods":["GET","HEAD"]},"mswdo-officer.users.applicants.edit":{"uri":"mswdo-officer\/users\/applicants\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
