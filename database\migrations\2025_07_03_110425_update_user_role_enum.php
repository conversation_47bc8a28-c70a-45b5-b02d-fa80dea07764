<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we'll change the role column to a string type to allow any role value
        Schema::table('users', function (Blueprint $table) {
            $table->string('role')->default('applicant')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is irreversible for SQLite, but we'll leave it as string
        // since enum constraints don't work well with SQLite anyway
    }
};
