<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Profile;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Superadmin (MSWDO Officer)
        $superadmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'mswdo-officer',
            'status' => 'verified',
            'email_verified_at' => now(),
            'phone' => '09123456789',
        ]);

        Profile::create([
            'user_id' => $superadmin->id,
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'birth_date' => '1990-01-01',
            'gender' => 'other',
            'contact_number' => '09123456789',
            'barangay' => 'Poblacion',
            'street_address' => 'Municipal Hall',
            'civil_status' => 'single',
            'is_pwd' => false,
            'is_senior_citizen' => false,
            'is_solo_parent' => false,
        ]);

        // Create Social Worker
        $admin = User::create([
            'name' => 'Social Worker Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'social-worker',
            'status' => 'verified',
            'email_verified_at' => now(),
            'phone' => '***********',
        ]);

        Profile::create([
            'user_id' => $admin->id,
            'first_name' => 'Social',
            'middle_name' => 'Worker',
            'last_name' => 'Admin',
            'birth_date' => '1995-01-01',
            'gender' => 'other',
            'contact_number' => '***********',
            'barangay' => 'Poblacion',
            'street_address' => 'MSWD Office',
            'civil_status' => 'single',
            'is_pwd' => false,
            'is_senior_citizen' => false,
            'is_solo_parent' => false,
        ]);

        // Create a test applicant account
        $client = User::create([
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'applicant',
            'status' => 'verified',
            'email_verified_at' => now(),
            'phone' => '***********',
        ]);

        Profile::create([
            'user_id' => $client->id,
            'first_name' => 'Juan',
            'middle_name' => 'Dela',
            'last_name' => 'Cruz',
            'birth_date' => '1998-05-15',
            'gender' => 'male',
            'contact_number' => '***********',
            'barangay' => 'San Juan',
            'street_address' => '123 Main Street',
            'civil_status' => 'single',
            'occupation' => 'Freelancer',
            'educational_attainment' => 'College Graduate',
            'is_pwd' => false,
            'is_senior_citizen' => false,
            'is_solo_parent' => true,
        ]);
    }
}
