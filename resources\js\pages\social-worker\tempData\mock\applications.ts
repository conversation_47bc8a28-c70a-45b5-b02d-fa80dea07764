import {
  Application,
  Appointment,
  BFACESApplication,
  Case,
  Client,
  DashboardData,
  Service,
  Verification,
  ServiceStats,
  CaseStats,
  ScheduleItem,
  AuthUser
} from '../types';

// Dashboard Data
export const dashboardData: DashboardData = {
  totalCases: 156,
  activeCases: 42,
  pendingVerifications: 23,
  urgentCases: 8,
  todayAppointments: 5,
  pendingApplications: 18,
  resolvedToday: 12,
  recentActivity: [
    {
      type: 'case_assigned',
      description: 'New medical assistance case assigned',
      client: '<PERSON>',
      time: '1 hour ago'
    },
    {
      type: 'verification_completed',
      description: 'Residency verification completed',
      client: '<PERSON>',
      time: '2 hours ago'
    },
    {
      type: 'application_approved',
      description: 'BFACES application approved',
      client: '<PERSON>',
      time: '3 hours ago'
    }
  ]
};

// Applications Data
export const applications: Application[] = [
  {
    id: "APP001",
    clientId: "1",
    clientName: "Juan Dela Cruz",
    service: "Medical Assistance",
    status: "pending",
    priority: "high",
    submittedAt: "2024-04-08",
    lastUpdated: "2024-04-08",
    notes: "Requesting assistance for surgery costs"
  },
  {
    id: "APP002",
    clientId: "2",
    clientName: "Maria Santos",
    service: "Burial Assistance",
    status: "in_progress",
    priority: "high",
    submittedAt: "2024-04-07",
    lastUpdated: "2024-04-08",
    assignedTo: "Pedro Manalo",
    notes: "Father passed away on April 5, requires immediate assistance"
  },
  {
    id: "APP003",
    clientId: "3",
    clientName: "Pedro Reyes",
    service: "Educational Aid",
    status: "approved",
    priority: "medium",
    submittedAt: "2024-04-05",
    lastUpdated: "2024-04-07",
    assignedTo: "Maria Garcia",
    notes: "Approved for PHP 3,000 educational assistance"
  },
  {
    id: "APP004",
    clientId: "4",
    clientName: "Juana Manalo",
    service: "Medical Assistance",
    status: "rejected",
    priority: "low",
    submittedAt: "2024-04-03",
    lastUpdated: "2024-04-06",
    assignedTo: "Pedro Manalo",
    notes: "Incomplete documentation. Missing medical certificate."
  },
  {
    id: "APP005",
    clientId: "5",
    clientName: "Roberto Garcia",
    service: "Senior Citizen Aid",
    status: "approved",
    priority: "low",
    submittedAt: "2024-04-01",
    lastUpdated: "2024-04-05",
    assignedTo: "Maria Garcia",
    notes: "Successfully processed. Senior ID and benefits package provided."
  }
];

// Appointments Data
export const appointments: Appointment[] = [
  {
    id: "1",
    clientName: "Juan Dela Cruz",
    purpose: "BFACES Interview",
    date: "2024-04-10",
    time: "09:00",
    location: "Office 1",
    status: "scheduled",
    type: "bfaces",
    notes: "Initial interview for BFACES assistance",
    documents: ["Application Form", "Valid ID", "Proof of Income"]
  },
  {
    id: "2",
    clientName: "Maria Santos",
    purpose: "Document Verification",
    date: "2024-04-10",
    time: "14:00",
    location: "Office 2",
    status: "completed",
    type: "senior-citizen",
    notes: "Follow-up interview for senior citizen benefits",
    documents: ["Senior ID", "Medical Records"],
    recommendation: "approve"
  },
  {
    id: "3",
    clientName: "Pedro Reyes",
    purpose: "Medical Assistance Follow-up",
    date: "2024-04-10",
    time: "11:00",
    location: "Office 1",
    status: "completed",
    type: "medical",
    notes: "Medical assistance interview",
    documents: ["Medical Certificate", "Hospital Bills"],
    recommendation: "pending"
  },
  {
    id: "4",
    clientName: "Ana Garcia",
    purpose: "Senior Citizen Aid Interview",
    date: "2024-04-10",
    time: "14:30",
    location: "Office 3",
    status: "no-show",
    type: "senior-citizen",
    notes: "Initial interview for senior citizen benefits",
    documents: ["Senior ID Application", "Proof of Residency"],
  }
];

// Clients Data
export const clients: Client[] = [
  {
    id: "1",
    name: "Juan Dela Cruz",
    email: "<EMAIL>",
    phone: "09123456789",
    barangay: "San Miguel",
    address: "123 Main St., San Miguel, Balagtas",
    status: "verified",
    joinedDate: "2024-01-15",
    lastActive: "2024-04-08",
    applications: 3,
    servicesReceived: 2,
    pendingVerifications: 0,
    bfacesStatus: "verified"
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "09234567890",
    barangay: "Wawa",
    address: "456 Secondary St., Wawa, Balagtas",
    status: "pending",
    joinedDate: "2024-03-20",
    lastActive: "2024-04-07",
    applications: 1,
    servicesReceived: 0,
    pendingVerifications: 1,
    bfacesStatus: "pending"
  },
  {
    id: "3",
    name: "Pedro Reyes",
    email: "<EMAIL>",
    phone: "09345678901",
    barangay: "Longos",
    address: "789 Third St., Longos, Balagtas",
    status: "inactive",
    joinedDate: "2023-11-10",
    lastActive: "2024-02-15",
    applications: 2,
    servicesReceived: 1,
    pendingVerifications: 0,
    bfacesStatus: "none"
  },
  {
    id: "4",
    name: "Juana Manalo",
    email: "<EMAIL>",
    phone: "09456789012",
    barangay: "Pinagbarilan",
    address: "101 Fourth St., Pinagbarilan, Balagtas",
    status: "verified",
    joinedDate: "2023-12-05",
    lastActive: "2024-04-06",
    applications: 5,
    servicesReceived: 3,
    pendingVerifications: 1,
    bfacesStatus: "verified"
  }
];

// Services Data
export const services: Service[] = [
  {
    id: 1,
    title: "Medical Assistance",
    description: "Financial assistance for medical expenses",
    eligibility: ["Resident of Balagtas", "Low-income family", "Valid medical documents"],
    requirements: ["Medical certificate", "Valid ID", "Proof of income"],
    process: ["Submit application", "Initial interview", "Document verification", "Approval"],
    status: "available",
    schedule: "Monday to Friday, 8:00 AM - 5:00 PM",
    location: "Main Office",
    maxBeneficiaries: 100,
    currentBeneficiaries: 45,
    applications: [],
    createdAt: "2024-01-01",
    updatedAt: "2024-04-08"
  },
  {
    id: 2,
    title: "Educational Aid",
    description: "Support for educational expenses",
    eligibility: ["Student", "Resident of Balagtas", "Good academic standing"],
    requirements: ["School ID", "Report card", "Certificate of enrollment"],
    process: ["Submit requirements", "Interview", "Assessment", "Approval"],
    status: "available",
    schedule: "Monday to Friday, 8:00 AM - 5:00 PM",
    location: "Education Office",
    maxBeneficiaries: 200,
    currentBeneficiaries: 120,
    applications: [],
    createdAt: "2024-01-01",
    updatedAt: "2024-04-08"
  }
];

// Cases Data
export const cases: Case[] = [
  {
    id: 1,
    client: {
      id: 1,
      name: "Juan Dela Cruz",
      email: "<EMAIL>",
      phone: "09123456789",
      barangay: "San Miguel",
      address: "123 Main St."
    },
    type: "service",
    status: "in_progress",
    priority: "high",
    assignedAt: "2024-04-01",
    lastUpdated: "2024-04-08",
    nextFollowUp: "2024-04-15",
    description: "Medical assistance for surgery",
    notes: ["Initial assessment completed", "Documents verified"]
  }
];

// Statistics
export const serviceStats: ServiceStats = {
  totalApplications: 450,
  approvedApplications: 320,
  pendingApplications: 80,
  rejectedApplications: 50,
  serviceTypes: [
    { name: "Medical", count: 150 },
    { name: "Educational", count: 200 },
    { name: "Burial", count: 100 }
  ],
  monthlyApplications: [
    { month: "January", count: 45 },
    { month: "February", count: 52 },
    { month: "March", count: 48 },
    { month: "April", count: 35 }
  ]
};

export const caseStats: CaseStats = {
  totalCases: 320,
  resolvedCases: 280,
  pendingCases: 30,
  urgentCases: 10,
  caseTypes: [
    { type: "Medical", count: 120 },
    { type: "Educational", count: 100 },
    { type: "BFACES", count: 100 }
  ],
  resolutionTimes: [
    { range: "1-3 days", count: 150 },
    { range: "4-7 days", count: 100 },
    { range: "8+ days", count: 70 }
  ]
};

export const verifications: Verification[] = [
  {
    id: "1",
    clientName: "Juan Dela Cruz",
    documentType: "medical",
    status: "pending",
    submittedAt: "2024-04-08",
    documentUrl: "/documents/medical-cert-001.pdf",
    urgency: "urgent"
  },
  {
    id: "2",
    clientName: "Maria Santos",
    documentType: "residency",
    status: "verified",
    submittedAt: "2024-04-07",
    verifiedAt: "2024-04-08",
    verifiedBy: "Admin User",
    documentUrl: "/documents/residency-cert-002.pdf",
    urgency: "urgent"
  },
  {
    id: "3",
    clientName: "Pedro Reyes",
    documentType: "medical",
    status: "needs-review",
    submittedAt: "2024-04-07",
    notes: "Additional documentation needed",
    documentUrl: "/documents/medical-3.pdf",
    urgency: "urgent",
  }
];

export const dashboardMetrics = {
  totalApplications: {
    value: 128,
    change: "+14 from last month"
  },
  pendingReview: {
    value: 23,
    change: "+7 since yesterday"
  },
  activeServices: {
    value: 12,
    change: "2 need attention"
  },
  todayAppointments: {
    value: 8,
    change: "3 completed"
  }
};

// Social Worker User Data
export const ALL_PERMISSIONS = [
  "case_management",
  "residency_verification",
  "service_requests",
  "interviews",
  "client_management",
  "reports_access",
  "bfaces_management",
  "appointment_management",
  "verification_management"
] as const;

export const socialWorkerUser: AuthUser = {
  id: "1",
  name: "Social Worker User",
  email: "<EMAIL>",
  permissions: [...ALL_PERMISSIONS]
};