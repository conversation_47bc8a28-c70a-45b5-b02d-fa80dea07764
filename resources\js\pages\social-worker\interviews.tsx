import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Clock, User, MapPin, CheckCircle, XCircle, Search, Filter, FileText } from "lucide-react";
import { useState } from "react";
import { type BreadcrumbItem } from "@/types";

interface Interview {
  id: string;
  clientName: string;
  type: "bfaces" | "senior-citizen" | "medical" | "educational";
  status: "scheduled" | "completed" | "cancelled" | "no-show";
  date: string;
  time: string;
  location: string;
  notes: string;
  documents: string[];
  recommendation?: "approve" | "reject" | "pending";
}

// Dummy data for demonstration
const dummyInterviews: Interview[] = [
  {
    id: "1",
    clientName: "Juan Dela Cruz",
    type: "bfaces",
    status: "scheduled",
    date: "2024-04-10",
    time: "09:00",
    location: "Office 1",
    notes: "Initial interview for BFACES assistance",
    documents: ["Application Form", "Valid ID", "Proof of Income"],
  },
  {
    id: "2",
    clientName: "Maria Santos",
    type: "senior-citizen",
    status: "completed",
    date: "2024-04-09",
    time: "14:00",
    location: "Office 2",
    notes: "Follow-up interview for senior citizen benefits",
    documents: ["Senior ID", "Medical Records"],
    recommendation: "approve",
  },
  {
    id: "3",
    clientName: "Pedro Reyes",
    type: "medical",
    status: "completed",
    date: "2024-04-09",
    time: "11:00",
    location: "Office 1",
    notes: "Medical assistance interview",
    documents: ["Medical Certificate", "Hospital Bills"],
    recommendation: "pending",
  },
];

export default function Interviews() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "Interview Management", href: "/admin/interviews" },
  ];

  const filteredInterviews = dummyInterviews.filter(interview => {
    const matchesStatus = statusFilter === "all" || interview.status === statusFilter;
    const matchesType = typeFilter === "all" || interview.type === typeFilter;
    const matchesSearch = interview.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      interview.notes.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesType && matchesSearch;
  });

  const getStatusBadge = (status: Interview["status"]) => {
    const variants: Record<Interview["status"], "default" | "secondary" | "destructive" | "outline"> = {
      scheduled: "default",
      completed: "secondary",
      cancelled: "destructive",
      "no-show": "outline",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getRecommendationBadge = (recommendation?: Interview["recommendation"]) => {
    if (!recommendation) return null;
    const variants = {
      approve: "secondary",
      reject: "destructive",
      pending: "outline",
    } as const;
    return <Badge variant={variants[recommendation]}>{recommendation}</Badge>;
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Interviews" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Interview Management</h1>
          <Button>Schedule New Interview</Button>
        </div>

        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or notes..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type">Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="bfaces">BFACES</SelectItem>
                  <SelectItem value="senior-citizen">Senior Citizen</SelectItem>
                  <SelectItem value="medical">Medical</SelectItem>
                  <SelectItem value="educational">Educational</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <div className="space-y-4">
          {filteredInterviews.map((interview) => (
            <Card key={interview.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex gap-4">
                  <div className="mt-1">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{interview.clientName}</h3>
                      {getStatusBadge(interview.status)}
                      {interview.recommendation && getRecommendationBadge(interview.recommendation)}
                    </div>
                    <div className="mt-2 space-y-1 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="capitalize">{interview.type} Interview</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{interview.date} at {interview.time}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{interview.location}</span>
                      </div>
                      {interview.notes && (
                        <p className="mt-2 text-sm text-gray-600">{interview.notes}</p>
                      )}
                      {interview.documents.length > 0 && (
                        <div className="mt-2">
                          <span className="text-sm font-medium">Required Documents:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {interview.documents.map((doc, index) => (
                              <Badge key={index} variant="outline">{doc}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {interview.status === "scheduled" && (
                    <>
                      <Button size="sm" variant="outline">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Start Interview
                      </Button>
                      <Button size="sm" variant="outline">
                        <XCircle className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                    </>
                  )}
                  {interview.status === "completed" && !interview.recommendation && (
                    <Button size="sm">Add Recommendation</Button>
                  )}
                </div>
              </div>
            </Card>
          ))}

          {filteredInterviews.length === 0 && (
            <Card className="p-8">
              <div className="text-center text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No interviews found</h3>
                <p>There are no interviews matching your filters.</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
} 