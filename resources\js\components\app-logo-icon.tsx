import { SVGAttributes } from 'react';

interface AppLogoIconProps extends SVGAttributes<SVGElement> {
    variant?: 'light' | 'dark';
    showText?: boolean;
}

export default function AppLogoIcon({ variant = 'light', showText = true, ...props }: AppLogoIconProps) {
    const isLight = variant === 'light';
    const logoSrc = isLight ? "/images/main-logo.png" : "/images/main-logo-white.png";
    const textColor = isLight ? "text-purple-900" : "text-white";
    const subtextColor = isLight ? "text-purple-600" : "text-white";

    return (
        <div className="flex items-center gap-2">
            <img
                src={logoSrc}
                alt="Balagtas SocialCare Logo"
                className="h-10 w-auto"
                onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // Fallback to the other logo variant if image fails to load
                    target.src = isLight ? "/images/main-logo-white.png" : "/images/main-logo.png";
                    console.error('Logo image failed to load, using fallback');
                }}
            />
            {showText && (
                <div className="grid flex-1 text-left text-sm">
                    <span className={`mb-0.5 truncate leading-none font-semibold ${textColor}`}>
                        Balagtas SocialCare
                    </span>
                    <span className={`text-xs ${subtextColor}`}>
                        Municipality of Balagtas
                    </span>
                </div>
            )}
        </div>
    );
}
