import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { type BreadcrumbItem } from "@/types";
import { Head, useForm } from "@inertiajs/react";
import { CheckCircle, Clock, Upload, AlertCircle, HelpCircle } from "lucide-react";

type VerificationStatus = "pending" | "verified" | "rejected";

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: "Dashboard",
        href: "/dashboard",
    },
    {
        title: "Residency Verification",
        href: "/residency-verification",
    },
];

// Dummy data
const verificationStatus: VerificationStatus = "pending";
const uploadedDocuments = [
    {
        id: 1,
        name: "Electric Bill",
        fileName: "electric_bill_march_2023.pdf",
        uploadDate: "2023-04-02",
        status: "pending", // 'pending', 'verified', 'rejected'
    },
];

// Helper functions
const getStatusIcon = (status: string) => {
    switch (status) {
        case "verified":
            return <CheckCircle className="h-6 w-6 text-green-500" />;
        case "pending":
            return <Clock className="h-6 w-6 text-amber-500" />;
        case "rejected":
            return <AlertCircle className="h-6 w-6 text-red-500" />;
        default:
            return <HelpCircle className="h-6 w-6 text-gray-500" />;
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case "verified":
            return "Verified";
        case "pending":
            return "Pending Verification";
        case "rejected":
            return "Verification Rejected";
        default:
            return "Unknown";
    }
};

const getDocumentStatusText = (status: string) => {
    switch (status) {
        case "verified":
            return "Verified";
        case "pending":
            return "Under Review";
        case "rejected":
            return "Rejected";
        default:
            return "Unknown";
    }
};

export default function ResidencyVerification() {
    const { data, setData, post, processing, errors } = useForm({
        documentType: "",
        documentFile: null as File | null,
    });

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setData("documentFile", e.target.files[0]);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // In a real app, you would submit to the server
        alert("Document upload simulated! In a real app, this would be sent to the server.");
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Residency Verification" />

            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">Residency Verification</h1>
                </div>

                {/* Status Card */}
                <Card>
                    <CardHeader className="flex flex-row items-center gap-4">
                        {getStatusIcon(verificationStatus)}
                        <div>
                            <CardTitle>Verification Status: {getStatusText(verificationStatus)}</CardTitle>
                            <CardDescription>
                                {verificationStatus === "pending"
                                    ? "Your documents are being reviewed. This process typically takes 1-2 business days."
                                    : verificationStatus === "verified"
                                    ? "Your residency in Balagtas has been verified. You now have access to all services."
                                    : "Your verification was rejected. Please check the document requirements and submit valid proof of residency."}
                            </CardDescription>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="mb-2 flex justify-between text-sm">
                            <span>Verification Progress</span>
                            <span className="font-medium">
                                {verificationStatus === "verified" ? "Complete" : verificationStatus === "rejected" ? "Rejected" : "In Progress"}
                            </span>
                        </div>
                        <Progress
                            value={verificationStatus === "verified" ? 100 : verificationStatus === "rejected" ? 100 : 50}
                            className={`h-2 ${
                                verificationStatus === "verified"
                                    ? "bg-green-100"
                                    : verificationStatus === "rejected"
                                    ? "bg-red-100"
                                    : "bg-amber-100"
                            }`}
                        />
                    </CardContent>
                </Card>

                <Tabs defaultValue="upload">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="upload">Upload Documents</TabsTrigger>
                        <TabsTrigger value="status">Document Status</TabsTrigger>
                    </TabsList>
                    <TabsContent value="upload" className="mt-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Upload Proof of Residency</CardTitle>
                                <CardDescription>
                                    Please upload at least one of the following documents to verify your residency in Balagtas:
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <h3 className="font-medium">Accepted Documents</h3>
                                    <ul className="list-inside list-disc text-sm text-muted-foreground">
                                        <li>Latest Electricity Bill (must show your name and Balagtas address)</li>
                                        <li>Latest Water Bill (must show your name and Balagtas address)</li>
                                        <li>Barangay Certificate of Residency (issued within the last 6 months)</li>
                                        <li>Government-issued ID with Balagtas address (Voter's ID, Driver's License, etc.)</li>
                                    </ul>
                                </div>

                                <Separator />

                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="space-y-2">
                                        <label htmlFor="documentType" className="text-sm font-medium">
                                            Document Type
                                        </label>
                                        <select
                                            id="documentType"
                                            value={data.documentType}
                                            onChange={(e) => setData("documentType", e.target.value)}
                                            className="w-full rounded-md border border-input bg-background px-3 py-2"
                                            required
                                        >
                                            <option value="">Select Document Type</option>
                                            <option value="electric_bill">Electricity Bill</option>
                                            <option value="water_bill">Water Bill</option>
                                            <option value="barangay_certificate">Barangay Certificate</option>
                                            <option value="government_id">Government ID</option>
                                        </select>
                                        {errors.documentType && <p className="text-sm text-red-500">{errors.documentType}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <label htmlFor="documentFile" className="text-sm font-medium">
                                            Upload Document
                                        </label>
                                        <div className="flex items-center justify-center w-full">
                                            <label
                                                htmlFor="documentFile"
                                                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted/50"
                                            >
                                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                    <Upload className="h-6 w-6 text-muted-foreground mb-2" />
                                                    <p className="mb-2 text-sm text-muted-foreground">
                                                        <span className="font-semibold">Click to upload</span> or drag and drop
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">PDF, JPG, or PNG (MAX. 5MB)</p>
                                                </div>
                                                <input
                                                    id="documentFile"
                                                    type="file"
                                                    className="hidden"
                                                    accept=".pdf,.jpg,.jpeg,.png"
                                                    onChange={handleFileChange}
                                                    required
                                                />
                                            </label>
                                        </div>
                                        {data.documentFile && (
                                            <p className="text-sm">Selected file: {data.documentFile.name}</p>
                                        )}
                                        {errors.documentFile && <p className="text-sm text-red-500">{errors.documentFile}</p>}
                                    </div>

                                    <Button type="submit" disabled={processing}>
                                        Upload Document
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    <TabsContent value="status" className="mt-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Document Status</CardTitle>
                                <CardDescription>
                                    Track the status of your uploaded documents for residency verification.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {uploadedDocuments.length > 0 ? (
                                    <div className="space-y-4">
                                        {uploadedDocuments.map((doc) => (
                                            <div key={doc.id} className="flex items-start justify-between rounded-lg border p-4">
                                                <div className="space-y-1">
                                                    <p className="font-medium">{doc.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Filename: {doc.fileName}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Uploaded on: {doc.uploadDate}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {getStatusIcon(doc.status)}
                                                    <span className="text-sm font-medium">
                                                        {getDocumentStatusText(doc.status)}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 text-center">
                                        <HelpCircle className="h-10 w-10 text-muted-foreground mb-2" />
                                        <h3 className="text-lg font-medium">No Documents Uploaded</h3>
                                        <p className="text-sm text-muted-foreground max-w-md">
                                            You haven't uploaded any documents for residency verification yet. Please
                                            upload at least one valid document to proceed.
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                            <CardFooter>
                                <p className="text-xs text-muted-foreground">
                                    Need help? Contact the Balagtas SocialCare office at (044) 123-4567 or email
                                    <EMAIL>
                                </p>
                            </CardFooter>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
} 