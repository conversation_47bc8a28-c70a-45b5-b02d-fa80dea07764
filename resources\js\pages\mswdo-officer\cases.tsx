import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Search, Plus, Filter, FileSpreadsheet,
    UserCheck, AlertTriangle, Home, Baby, Users, Shield
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

// Import case table components
import ReformistTable from '@/components/cases/ReformistTable';
import BahayKanlunganTable from '@/components/cases/BahayKanlunganTable';
import ChildrenProtectionTable from '@/components/cases/ChildrenProtectionTable';
import ChildrenConflictTable from '@/components/cases/ChildrenConflictTable';
import WomenDifficultTable from '@/components/cases/WomenDifficultTable';
import MenDifficultTable from '@/components/cases/MenDifficultTable';
import OSAECTable from '@/components/cases/OSAECTable';
import ChildAtRiskTable from '@/components/cases/ChildAtRiskTable';
const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Case and Service Management',
        href: '#',
    },
    {
        title: 'Case Management',
        href: '/mswdo-officer/cases',
    },
];

// Case type definitions with their specific data structures
const caseTypes = [
    {
        id: 'reformist',
        name: 'LIST OF REFORMIST',
        icon: UserCheck,
        description: 'Rehabilitation and reform cases'
    },
    {
        id: 'bahay-kanlungan',
        name: 'MASTERLIST ADMITTED IN BAHAY KANLUNGAN',
        icon: Home,
        description: 'Shelter admission tracking'
    },
    {
        id: 'children-protection',
        name: 'CHILDREN IN NEED SPECIAL PROTECTION',
        icon: Shield,
        description: 'Child protection cases'
    },
    {
        id: 'children-conflict',
        name: 'CHILDREN IN CONFLICT WITH THE LAW',
        icon: AlertTriangle,
        description: 'Juvenile justice cases'
    },
    {
        id: 'women-difficult',
        name: 'Cases of Women in Especially Difficult Circumstances',
        icon: Users,
        description: 'Women protection cases'
    },
    {
        id: 'men-difficult',
        name: 'Cases of Men in Especially Difficult Circumstances',
        icon: Users,
        description: 'Men protection cases'
    },
    {
        id: 'osaec',
        name: 'Inventory of Cases of Online Sexual Abuse and Exploitation of Children and Child Sexual Abuse or Exploitation Materials',
        icon: Shield,
        description: 'OSAEC cases'
    },
    {
        id: 'child-at-risk',
        name: 'MASTERLIST OF CHILD AT RISK',
        icon: Baby,
        description: 'At-risk children tracking'
    }
];

// Import sample data
import {
    reformistCases,
    bahayKanlunganCases,
    childrenProtectionCases,
    childrenConflictCases,
    womenDifficultCases,
    menDifficultCases,
    osaecCases,
    childAtRiskCases
} from '@/data/casesData';

// Simple statistics calculation
const getAllCasesCount = () => {
    return reformistCases.length +
           bahayKanlunganCases.length +
           childrenProtectionCases.length +
           childrenConflictCases.length +
           womenDifficultCases.length +
           menDifficultCases.length +
           osaecCases.length +
           childAtRiskCases.length;
};

const stats = {
    totalCases: getAllCasesCount(),
    reformistCases: reformistCases.length,
    bahayKanlunganCases: bahayKanlunganCases.length,
    childrenProtectionCases: childrenProtectionCases.length,
    childrenConflictCases: childrenConflictCases.length,
    womenDifficultCases: womenDifficultCases.length,
    menDifficultCases: menDifficultCases.length,
    osaecCases: osaecCases.length,
    childAtRiskCases: childAtRiskCases.length
};

export default function CaseManagement() {
    const [activeFilter, setActiveFilter] = useState('reformist');

    // Get current case data based on active filter
    const getCurrentCaseData = () => {
        switch (activeFilter) {
            case 'reformist': return reformistCases;
            case 'bahay-kanlungan': return bahayKanlunganCases;
            case 'children-protection': return childrenProtectionCases;
            case 'children-conflict': return childrenConflictCases;
            case 'women-difficult': return womenDifficultCases;
            case 'men-difficult': return menDifficultCases;
            case 'osaec': return osaecCases;
            case 'child-at-risk': return childAtRiskCases;
            default: return reformistCases;
        }
    };

    // Render appropriate table component based on active filter
    const renderTableComponent = () => {
        const currentData = getCurrentCaseData();

        switch (activeFilter) {
            case 'reformist':
                return <ReformistTable cases={currentData} />;
            case 'bahay-kanlungan':
                return <BahayKanlunganTable cases={currentData} />;
            case 'children-protection':
                return <ChildrenProtectionTable cases={currentData} />;
            case 'children-conflict':
                return <ChildrenConflictTable cases={currentData} />;
            case 'women-difficult':
                return <WomenDifficultTable cases={currentData} />;
            case 'men-difficult':
                return <MenDifficultTable cases={currentData} />;
            case 'osaec':
                return <OSAECTable cases={currentData} />;
            case 'child-at-risk':
                return <ChildAtRiskTable cases={currentData} />;
            default:
                return <ReformistTable cases={currentData} />;
        }
    };



    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Case Management" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Case Management</h1>
                        <p className="text-purple-600 mt-2">Comprehensive case tracking and management system</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <FileSpreadsheet className="h-4 w-4" />
                            Export to Excel
                        </Button>
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                            <Link href={`/mswdo-officer/cases/${activeFilter}/new`}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add New Case
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalCases}</div>
                            <p className="text-sm text-purple-600">All recorded cases</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Reformist Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.reformistCases}</div>
                            <p className="text-sm text-blue-600">Rehabilitation cases</p>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Child Protection</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.childrenProtectionCases}</div>
                            <p className="text-sm text-green-600">Protection cases</p>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Current Type</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">{getCurrentCaseData().length}</div>
                            <p className="text-sm text-orange-600">Current filter cases</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search cases..."
                            className="pl-10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                </div>

                {/* Case Type Filtering Navigation */}
                <Card className="border-purple-200">
                    <CardContent className="p-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-2">
                            {caseTypes.map((caseType) => {
                                const IconComponent = caseType.icon;
                                const isActive = activeFilter === caseType.id;
                                return (
                                    <Button
                                        key={caseType.id}
                                        variant={isActive ? "default" : "outline"}
                                        onClick={() => setActiveFilter(caseType.id)}
                                        className={`flex flex-col items-center gap-1 p-3 h-auto text-xs ${
                                            isActive
                                                ? 'bg-purple-600 hover:bg-purple-700 text-white'
                                                : 'hover:bg-purple-50 border-purple-200'
                                        }`}
                                    >
                                        <IconComponent className="h-4 w-4" />
                                        <span className="text-center leading-tight">
                                            {caseType.name.length > 20
                                                ? `${caseType.name.substring(0, 20)}...`
                                                : caseType.name
                                            }
                                        </span>
                                    </Button>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>

                {/* Case Statistics */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                    <p className="text-sm text-gray-600">
                        Showing {getCurrentCaseData().length} cases for {caseTypes.find(ct => ct.id === activeFilter)?.name}
                    </p>
                    <div className="text-xs text-gray-500 sm:hidden">
                        Tap on any row for full details
                    </div>
                </div>

                {/* Dynamic Table Component */}
                {renderTableComponent()}
            </div>
        </AppLayout>
    );
}
