import { Head } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from "@/types";
import {
    FileSpreadsheet, Download, TrendingUp, BarChart3, PieChart,
    Calendar, MapPin, Users, DollarSign, FileText, Calculator,
    Target, AlertTriangle, CheckCircle, ArrowUpDown
} from 'lucide-react';

// Data interfaces for the Social Service Assistance Report
interface AssistanceData {
    barangay: string;
    burial: {
        beneficiaries: number;
        amount: number;
    };
    medical: {
        beneficiaries: number;
        amount: number;
    };
    financial: {
        beneficiaries: number;
        amount: number;
    };
    educational: {
        beneficiaries: number;
        amount: number;
    };
}

interface MonthlyReport {
    month: string;
    year: number;
    data: AssistanceData[];
    totalBeneficiaries: number;
    totalAmount: number;
}

interface DSSAnalytics {
    historicalTrends: {
        year: number;
        totalBudget: number;
        totalSpent: number;
        utilizationRate: number;
    }[];
    barangayAnalysis: {
        barangay: string;
        avgMonthlyDemand: number;
        peakMonth: string;
        recommendedAllocation: number;
        currentAllocation: number;
        variance: number;
    }[];
    budgetRecommendations: {
        category: string;
        currentBudget: number;
        recommendedBudget: number;
        reasoning: string;
        priority: 'high' | 'medium' | 'low';
    }[];
}

// Mock data for Social Service Assistance Report (February 2023)
const mockReportData: MonthlyReport = {
    month: "February",
    year: 2023,
    data: [
        {
            barangay: "WAWA",
            burial: { beneficiaries: 2, amount: 6000 },
            medical: { beneficiaries: 10, amount: 25000 },
            financial: { beneficiaries: 1, amount: 3000 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "SAN JUAN",
            burial: { beneficiaries: 2, amount: 4000 },
            medical: { beneficiaries: 10, amount: 25000 },
            financial: { beneficiaries: 1, amount: 3000 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "LONGOS",
            burial: { beneficiaries: 0, amount: 0 },
            medical: { beneficiaries: 3, amount: 7500 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "PANGINAY",
            burial: { beneficiaries: 3, amount: 7000 },
            medical: { beneficiaries: 19, amount: 45000 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "BOROL 1ST",
            burial: { beneficiaries: 3, amount: 6000 },
            medical: { beneficiaries: 16, amount: 43500 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "BOROL 2ND",
            burial: { beneficiaries: 2, amount: 4000 },
            medical: { beneficiaries: 6, amount: 16000 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "SANTOL",
            burial: { beneficiaries: 3, amount: 7000 },
            medical: { beneficiaries: 9, amount: 23000 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "PULONG GUBAT",
            burial: { beneficiaries: 1, amount: 2000 },
            medical: { beneficiaries: 8, amount: 20000 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        },
        {
            barangay: "DALIG",
            burial: { beneficiaries: 1, amount: 2000 },
            medical: { beneficiaries: 2, amount: 4000 },
            financial: { beneficiaries: 0, amount: 0 },
            educational: { beneficiaries: 0, amount: 0 }
        }
    ],
    totalBeneficiaries: 99,
    totalAmount: 247500
};

// Mock DSS Analytics Data
const mockDSSData: DSSAnalytics = {
    historicalTrends: [
        { year: 2020, totalBudget: 2500000, totalSpent: 2100000, utilizationRate: 84 },
        { year: 2021, totalBudget: 2800000, totalSpent: 2450000, utilizationRate: 87.5 },
        { year: 2022, totalBudget: 3200000, totalSpent: 2890000, utilizationRate: 90.3 },
        { year: 2023, totalBudget: 3500000, totalSpent: 2970000, utilizationRate: 84.9 }
    ],
    barangayAnalysis: [
        { barangay: "WAWA", avgMonthlyDemand: 34000, peakMonth: "December", recommendedAllocation: 408000, currentAllocation: 380000, variance: 28000 },
        { barangay: "SAN JUAN", avgMonthlyDemand: 32000, peakMonth: "November", recommendedAllocation: 384000, currentAllocation: 360000, variance: 24000 },
        { barangay: "PANGINAY", avgMonthlyDemand: 52000, peakMonth: "January", recommendedAllocation: 624000, currentAllocation: 580000, variance: 44000 }
    ],
    budgetRecommendations: [
        { category: "Medical Assistance", currentBudget: 1500000, recommendedBudget: 1750000, reasoning: "Increasing demand due to aging population", priority: "high" },
        { category: "Educational Assistance", currentBudget: 300000, recommendedBudget: 450000, reasoning: "More students requiring support", priority: "medium" },
        { category: "Burial Assistance", currentBudget: 500000, recommendedBudget: 520000, reasoning: "Slight increase to match inflation", priority: "low" }
    ]
};

export default function BudgetAllocation() {
    const [selectedMonth, setSelectedMonth] = useState("February");
    const [selectedYear, setSelectedYear] = useState("2023");
    const [activeTab, setActiveTab] = useState("report");

    const breadcrumbs: BreadcrumbItem[] = [
        { title: "Dashboard", href: "/mswdo-officer/dashboard" },
        { title: "Budget Allocation & DSS", href: "/mswdo-officer/budget" },
    ];

    // Helper functions
    const calculateBarangayTotal = (data: AssistanceData) => {
        return data.burial.amount + data.medical.amount + data.financial.amount + data.educational.amount;
    };

    const calculateBarangayBeneficiaries = (data: AssistanceData) => {
        return data.burial.beneficiaries + data.medical.beneficiaries + data.financial.beneficiaries + data.educational.beneficiaries;
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Budget Allocation & Decision Support System" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">
                            Budget Allocation & Decision Support System
                        </h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">
                            Social service assistance reporting and intelligent budget planning
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <FileSpreadsheet className="h-4 w-4 mr-2" />
                            Export Excel
                        </Button>
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Download className="h-4 w-4 mr-2" />
                            Export PDF
                        </Button>
                    </div>
                </div>
                {/* Main Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="report" className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Monthly Report
                        </TabsTrigger>
                        <TabsTrigger value="annual-planning" className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            Annual Planning DSS
                        </TabsTrigger>
                        <TabsTrigger value="barangay-allocation" className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            Barangay Allocation DSS
                        </TabsTrigger>
                    </TabsList>

                    {/* Monthly Report Tab */}
                    <TabsContent value="report" className="space-y-6">
                        <Card>
                            <CardHeader className="pb-4">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                    <div>
                                        <CardTitle className="text-xl font-bold text-purple-900">
                                            Social Service Assistance Report
                                        </CardTitle>
                                        <p className="text-sm text-muted-foreground mt-1">
                                            Comprehensive monthly assistance distribution report
                                        </p>
                                    </div>
                                    <div className="flex gap-2">
                                        <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                                            <SelectTrigger className="w-32">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="January">January</SelectItem>
                                                <SelectItem value="February">February</SelectItem>
                                                <SelectItem value="March">March</SelectItem>
                                                <SelectItem value="April">April</SelectItem>
                                                <SelectItem value="May">May</SelectItem>
                                                <SelectItem value="June">June</SelectItem>
                                                <SelectItem value="July">July</SelectItem>
                                                <SelectItem value="August">August</SelectItem>
                                                <SelectItem value="September">September</SelectItem>
                                                <SelectItem value="October">October</SelectItem>
                                                <SelectItem value="November">November</SelectItem>
                                                <SelectItem value="December">December</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <Select value={selectedYear} onValueChange={setSelectedYear}>
                                            <SelectTrigger className="w-24">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="2021">2021</SelectItem>
                                                <SelectItem value="2022">2022</SelectItem>
                                                <SelectItem value="2023">2023</SelectItem>
                                                <SelectItem value="2024">2024</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                {/* Report Header */}
                                <div className="text-center mb-6">
                                    <h2 className="text-lg font-bold text-red-600 uppercase tracking-wide">
                                        Social Service Assistance Given as of {selectedMonth} {selectedYear}
                                    </h2>
                                </div>

                                {/* Excel-like Table */}
                                <div className="border border-gray-300 rounded-lg overflow-hidden">
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-gray-100 border-b-2 border-gray-300">
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black py-3">
                                                    BARANGAY
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black py-3" colSpan={2}>
                                                    BURIAL
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black py-3" colSpan={2}>
                                                    MEDICAL
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black py-3" colSpan={2}>
                                                    FINANCIAL
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black py-3" colSpan={2}>
                                                    EDUCATIONAL
                                                </TableHead>
                                                <TableHead className="text-center font-bold text-black py-3" colSpan={2}>
                                                    GRAND TOTAL
                                                </TableHead>
                                            </TableRow>
                                            <TableRow className="bg-gray-50 border-b border-gray-300">
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    {/* Empty for barangay column */}
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    NO. OF BENEFICIARY
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    TOTAL AMOUNT
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    NO. OF BENEFICIARY
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    TOTAL AMOUNT
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    NO. OF BENEFICIARY
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    TOTAL AMOUNT
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    NO. OF BENEFICIARY
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    TOTAL AMOUNT
                                                </TableHead>
                                                <TableHead className="border-r border-gray-300 text-center font-bold text-black text-xs py-2">
                                                    BENEFICIARY
                                                </TableHead>
                                                <TableHead className="text-center font-bold text-black text-xs py-2">
                                                    AMOUNT
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {mockReportData.data.map((row, index) => (
                                                <TableRow key={row.barangay} className="border-b border-gray-300 hover:bg-gray-50">
                                                    <TableCell className="border-r border-gray-300 font-medium text-center py-2">
                                                        {row.barangay}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.burial.beneficiaries}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.burial.amount.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.medical.beneficiaries}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.medical.amount.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.financial.beneficiaries}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.financial.amount.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.educational.beneficiaries}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2">
                                                        {row.educational.amount.toLocaleString()}
                                                    </TableCell>
                                                    <TableCell className="border-r border-gray-300 text-center py-2 font-medium">
                                                        {calculateBarangayBeneficiaries(row)}
                                                    </TableCell>
                                                    <TableCell className="text-center py-2 font-medium">
                                                        {calculateBarangayTotal(row).toLocaleString()}
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                            {/* Total Row */}
                                            <TableRow className="bg-gray-100 border-t-2 border-gray-400 font-bold">
                                                <TableCell className="border-r border-gray-300 text-center py-3 font-bold">
                                                    TOTAL:
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.burial.beneficiaries, 0)}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.burial.amount, 0).toLocaleString()}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.medical.beneficiaries, 0)}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.medical.amount, 0).toLocaleString()}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.financial.beneficiaries, 0)}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.financial.amount, 0).toLocaleString()}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.educational.beneficiaries, 0)}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3">
                                                    {mockReportData.data.reduce((sum, row) => sum + row.educational.amount, 0).toLocaleString()}
                                                </TableCell>
                                                <TableCell className="border-r border-gray-300 text-center py-3 font-bold text-red-600">
                                                    {mockReportData.totalBeneficiaries}
                                                </TableCell>
                                                <TableCell className="text-center py-3 font-bold text-red-600">
                                                    {mockReportData.totalAmount.toLocaleString()}
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Annual Planning DSS Tab */}
                    <TabsContent value="annual-planning" className="space-y-6">
                        <div className="grid gap-6 lg:grid-cols-2">
                            {/* Historical Trends Analysis */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-purple-900">
                                        <TrendingUp className="h-5 w-5" />
                                        Historical Budget Analysis
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Year-over-year budget utilization trends
                                    </p>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {mockDSSData.historicalTrends.map((trend) => (
                                            <div key={trend.year} className="flex items-center justify-between p-3 border rounded-lg">
                                                <div>
                                                    <p className="font-medium">{trend.year}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Budget: {formatCurrency(trend.totalBudget)}
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">{formatCurrency(trend.totalSpent)}</p>
                                                    <Badge
                                                        variant={trend.utilizationRate >= 85 ? "default" : "secondary"}
                                                        className={trend.utilizationRate >= 85 ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}
                                                    >
                                                        {trend.utilizationRate}% utilized
                                                    </Badge>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                                        <h4 className="font-medium text-blue-900 mb-2">DSS Insight</h4>
                                        <p className="text-sm text-blue-800">
                                            Budget utilization has been consistently above 84%. Consider increasing
                                            the 2024 budget by 8-12% to meet growing demand.
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Budget Recommendations */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-purple-900">
                                        <Calculator className="h-5 w-5" />
                                        AI Budget Recommendations
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Data-driven budget allocation suggestions
                                    </p>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {mockDSSData.budgetRecommendations.map((rec, index) => (
                                            <div key={index} className="border rounded-lg p-4">
                                                <div className="flex items-center justify-between mb-2">
                                                    <h4 className="font-medium">{rec.category}</h4>
                                                    <Badge
                                                        variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'default' : 'secondary'}
                                                    >
                                                        {rec.priority} priority
                                                    </Badge>
                                                </div>
                                                <div className="grid grid-cols-2 gap-4 text-sm mb-2">
                                                    <div>
                                                        <p className="text-muted-foreground">Current</p>
                                                        <p className="font-medium">{formatCurrency(rec.currentBudget)}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-muted-foreground">Recommended</p>
                                                        <p className="font-medium text-green-600">{formatCurrency(rec.recommendedBudget)}</p>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-muted-foreground">{rec.reasoning}</p>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Budget Proposal Generation */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-purple-900">
                                    <FileText className="h-5 w-5" />
                                    Generate Budget Proposal
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                    Create data-driven budget proposals for next fiscal year
                                </p>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Fiscal Year</label>
                                        <Select defaultValue="2024">
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="2024">2024</SelectItem>
                                                <SelectItem value="2025">2025</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Growth Factor</label>
                                        <Select defaultValue="conservative">
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="conservative">Conservative (5%)</SelectItem>
                                                <SelectItem value="moderate">Moderate (10%)</SelectItem>
                                                <SelectItem value="aggressive">Aggressive (15%)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="space-y-2">
                                        <label className="text-sm font-medium">Include Inflation</label>
                                        <Select defaultValue="yes">
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="yes">Yes (3.2%)</SelectItem>
                                                <SelectItem value="no">No</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="flex items-end">
                                        <Button className="w-full bg-purple-600 hover:bg-purple-700">
                                            <Calculator className="h-4 w-4 mr-2" />
                                            Generate Proposal
                                        </Button>
                                    </div>
                                </div>

                                <div className="mt-6 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
                                    <FileText className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                                    <p className="text-sm text-muted-foreground">
                                        Generated budget proposal will appear here
                                    </p>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        Click "Generate Proposal" to create a comprehensive budget document
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Supplemental Budget Requests */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-purple-900">
                                    <AlertTriangle className="h-5 w-5" />
                                    Supplemental Budget Analysis
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                    Monitor and request additional budget allocations
                                </p>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-3">
                                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                                        <CardContent className="p-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                <AlertTriangle className="h-4 w-4 text-orange-600" />
                                                <span className="text-sm font-medium text-orange-900">Budget Depletion Risk</span>
                                            </div>
                                            <p className="text-2xl font-bold text-orange-900">68%</p>
                                            <p className="text-xs text-orange-700">Current utilization rate</p>
                                        </CardContent>
                                    </Card>

                                    <Card className="border-red-200 bg-gradient-to-br from-red-50 to-white">
                                        <CardContent className="p-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                <Calendar className="h-4 w-4 text-red-600" />
                                                <span className="text-sm font-medium text-red-900">Projected Depletion</span>
                                            </div>
                                            <p className="text-2xl font-bold text-red-900">Oct 2024</p>
                                            <p className="text-xs text-red-700">At current spending rate</p>
                                        </CardContent>
                                    </Card>

                                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                                        <CardContent className="p-4">
                                            <div className="flex items-center gap-2 mb-2">
                                                <DollarSign className="h-4 w-4 text-green-600" />
                                                <span className="text-sm font-medium text-green-900">Recommended Request</span>
                                            </div>
                                            <p className="text-2xl font-bold text-green-900">₱850K</p>
                                            <p className="text-xs text-green-700">Additional budget needed</p>
                                        </CardContent>
                                    </Card>
                                </div>

                                <div className="mt-4 flex gap-2">
                                    <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                        <FileText className="h-4 w-4 mr-2" />
                                        Generate Request Letter
                                    </Button>
                                    <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                        <Download className="h-4 w-4 mr-2" />
                                        Download Analysis Report
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Barangay Allocation DSS Tab */}
                    <TabsContent value="barangay-allocation" className="space-y-6">
                        {/* Barangay Performance Overview */}
                        <div className="grid gap-6 lg:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-purple-900">
                                        <BarChart3 className="h-5 w-5" />
                                        Barangay Demand Analysis
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                        Historical demand patterns by barangay
                                    </p>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {mockDSSData.barangayAnalysis.map((analysis) => (
                                            <div key={analysis.barangay} className="border rounded-lg p-4">
                                                <div className="flex items-center justify-between mb-2">
                                                    <h4 className="font-medium">{analysis.barangay}</h4>
                                                    <Badge variant={analysis.variance > 0 ? "destructive" : "default"}>
                                                        {analysis.variance > 0 ? "Under-allocated" : "Well-allocated"}
                                                    </Badge>
                                                </div>
                                                <div className="grid grid-cols-2 gap-4 text-sm">
                                                    <div>
                                                        <p className="text-muted-foreground">Avg Monthly Demand</p>
                                                        <p className="font-medium">{formatCurrency(analysis.avgMonthlyDemand)}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-muted-foreground">Peak Month</p>
                                                        <p className="font-medium">{analysis.peakMonth}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-muted-foreground">Current Allocation</p>
                                                        <p className="font-medium">{formatCurrency(analysis.currentAllocation)}</p>
                                                    </div>
                                                    <div>
                                                        <p className="text-muted-foreground">Recommended</p>
                                                        <p className="font-medium text-green-600">{formatCurrency(analysis.recommendedAllocation)}</p>
                                                    </div>
                                                </div>
                                                {analysis.variance > 0 && (
                                                    <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-800">
                                                        Shortage: {formatCurrency(analysis.variance)}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Budget Balancing Suggestions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-purple-900">
                                        <Target className="h-5 w-5" />
                                        Budget Balancing Recommendations
                                    </CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                        AI-powered allocation optimization
                                    </p>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="p-4 border rounded-lg bg-blue-50">
                                            <div className="flex items-center gap-2 mb-2">
                                                <CheckCircle className="h-4 w-4 text-blue-600" />
                                                <span className="font-medium text-blue-900">Optimization Opportunity</span>
                                            </div>
                                            <p className="text-sm text-blue-800 mb-3">
                                                Redistribute ₱96,000 from over-allocated barangays to high-demand areas
                                            </p>
                                            <div className="space-y-2 text-sm">
                                                <div className="flex justify-between">
                                                    <span>From: SANTOL (surplus)</span>
                                                    <span className="text-red-600">-₱45,000</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span>From: DALIG (surplus)</span>
                                                    <span className="text-red-600">-₱51,000</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span>To: PANGINAY (shortage)</span>
                                                    <span className="text-green-600">+₱44,000</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span>To: WAWA (shortage)</span>
                                                    <span className="text-green-600">+₱28,000</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span>To: SAN JUAN (shortage)</span>
                                                    <span className="text-green-600">+₱24,000</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-green-50">
                                            <div className="flex items-center gap-2 mb-2">
                                                <TrendingUp className="h-4 w-4 text-green-600" />
                                                <span className="font-medium text-green-900">Expected Impact</span>
                                            </div>
                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                                <div>
                                                    <p className="text-muted-foreground">Efficiency Gain</p>
                                                    <p className="font-medium text-green-800">+12.5%</p>
                                                </div>
                                                <div>
                                                    <p className="text-muted-foreground">Coverage Improvement</p>
                                                    <p className="font-medium text-green-800">+8.3%</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="p-4 border rounded-lg bg-yellow-50">
                                            <div className="flex items-center gap-2 mb-2">
                                                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                                <span className="font-medium text-yellow-900">Seasonal Considerations</span>
                                            </div>
                                            <p className="text-sm text-yellow-800">
                                                Consider increasing medical assistance allocation by 15% during flu season (June-August)
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Allocation Matrix */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-purple-900">
                                    <PieChart className="h-5 w-5" />
                                    Barangay Allocation Matrix
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                    Comprehensive view of current vs. recommended allocations
                                </p>
                            </CardHeader>
                            <CardContent>
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead className="font-bold">Barangay</TableHead>
                                                <TableHead className="text-center font-bold">Population</TableHead>
                                                <TableHead className="text-center font-bold">Current Allocation</TableHead>
                                                <TableHead className="text-center font-bold">Recommended</TableHead>
                                                <TableHead className="text-center font-bold">Variance</TableHead>
                                                <TableHead className="text-center font-bold">Priority</TableHead>
                                                <TableHead className="text-center font-bold">Action</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {[
                                                { name: "WAWA", population: 3250, current: 380000, recommended: 408000, priority: "High" },
                                                { name: "SAN JUAN", population: 2890, current: 360000, recommended: 384000, priority: "High" },
                                                { name: "LONGOS", population: 1850, current: 220000, recommended: 222000, priority: "Low" },
                                                { name: "PANGINAY", population: 4100, current: 580000, recommended: 624000, priority: "High" },
                                                { name: "BOROL 1ST", population: 3650, current: 450000, recommended: 438000, priority: "Medium" },
                                                { name: "BOROL 2ND", population: 2100, current: 280000, recommended: 252000, priority: "Low" },
                                                { name: "SANTOL", population: 2750, current: 350000, recommended: 330000, priority: "Medium" },
                                                { name: "PULONG GUBAT", population: 1950, current: 240000, recommended: 234000, priority: "Low" },
                                                { name: "DALIG", population: 1200, current: 180000, recommended: 144000, priority: "Low" }
                                            ].map((barangay) => {
                                                const variance = barangay.recommended - barangay.current;
                                                return (
                                                    <TableRow key={barangay.name}>
                                                        <TableCell className="font-medium">{barangay.name}</TableCell>
                                                        <TableCell className="text-center">{barangay.population.toLocaleString()}</TableCell>
                                                        <TableCell className="text-center">{formatCurrency(barangay.current)}</TableCell>
                                                        <TableCell className="text-center">{formatCurrency(barangay.recommended)}</TableCell>
                                                        <TableCell className="text-center">
                                                            <span className={variance > 0 ? "text-red-600" : variance < 0 ? "text-green-600" : "text-gray-600"}>
                                                                {variance > 0 ? "+" : ""}{formatCurrency(Math.abs(variance))}
                                                            </span>
                                                        </TableCell>
                                                        <TableCell className="text-center">
                                                            <Badge
                                                                variant={barangay.priority === 'High' ? 'destructive' : barangay.priority === 'Medium' ? 'default' : 'secondary'}
                                                            >
                                                                {barangay.priority}
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell className="text-center">
                                                            <Button variant="outline" size="sm" className="text-xs">
                                                                <ArrowUpDown className="h-3 w-3 mr-1" />
                                                                Adjust
                                                            </Button>
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            })}
                                        </TableBody>
                                    </Table>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Generate Reports */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-purple-900">
                                    <FileText className="h-5 w-5" />
                                    Generate Allocation Reports
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                    Create comprehensive reports for budget allocation decisions
                                </p>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                    <Button className="bg-purple-600 hover:bg-purple-700">
                                        <Download className="h-4 w-4 mr-2" />
                                        Allocation Summary
                                    </Button>
                                    <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                        <FileSpreadsheet className="h-4 w-4 mr-2" />
                                        Detailed Analysis
                                    </Button>
                                    <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                        <BarChart3 className="h-4 w-4 mr-2" />
                                        Visual Dashboard
                                    </Button>
                                    <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                        <Users className="h-4 w-4 mr-2" />
                                        Impact Assessment
                                    </Button>
                                </div>

                                <div className="mt-4 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
                                    <Download className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                                    <p className="text-sm text-muted-foreground">
                                        Generated reports will be available for download here
                                    </p>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        Select a report type above to generate comprehensive allocation documentation
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}