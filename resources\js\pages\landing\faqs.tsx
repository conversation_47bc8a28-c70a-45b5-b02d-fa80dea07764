import React, { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import LandingLayout from '@/components/landing-layout';
import LandingNav from '@/components/landing-nav';
import { 
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from '@/components/ui/card';
import { 
    UserIcon, 
    HelpCircleIcon, 
    FileTextIcon, 
    ShieldCheckIcon, 
    ClockIcon,
    HeartIcon,
    HandHeartIcon
} from 'lucide-react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

// Skeleton components
const FAQCategorySkeleton = () => (
    <div className="mb-8">
        <Skeleton width={200} height={32} className="mb-4" />
        <div className="space-y-4">
            {Array(4).fill(null).map((_, index) => (
                <div key={index} className="border rounded-lg p-4">
                    <Skeleton height={24} className="mb-2" />
                    <Skeleton count={2} />
                </div>
            ))}
        </div>
    </div>
);

export default function FAQs() {
    const { auth } = usePage<SharedData>().props;
    const [isLoading, setIsLoading] = useState(false);

    // State to track open accordion items
    const [openItems, setOpenItems] = useState<{ [key: string]: boolean }>({});

    const toggleItem = (itemId: string) => {
        setOpenItems(prev => ({
            ...prev,
            [itemId]: !prev[itemId]
        }));
    };

    const faqCategories = [
        {
            title: "General Questions",
            items: [
                {
                    question: "What is Balagtas SocialCare?",
                    answer: "Balagtas SocialCare is the official social services platform for residents of Balagtas, providing various forms of assistance including medical, burial, and senior citizen aid."
                },
                {
                    question: "Who can apply for services?",
                    answer: "Verified residents of Balagtas who have completed the residency verification process and BFACES application can apply for services."
                },
                {
                    question: "Are there any restrictions on service availment?",
                    answer: "Yes. After claiming a service, you will be placed under a 12-month cooldown period for that specific service. Additionally, you can only apply for one service at a time and cannot avail services simultaneously."
                }
            ]
        },
        {
            title: "Application Process",
            items: [
                {
                    question: "How do I start my application?",
                    answer: "First, create an account and complete the residency verification process. Once verified, you can proceed with the BFACES application, which is required before accessing any specific services."
                },
                {
                    question: "What is BFACES?",
                    answer: "BFACES (Balagtas Financial Assistance for Crisis and Emergency Situations) is our core program that evaluates your eligibility for social services based on your household information and crisis situation."
                },
                {
                    question: "Can I apply for multiple services at once?",
                    answer: "No, you can only apply for one service at a time. You must complete or withdraw your current service application before applying for another service."
                }
            ]
        },
        {
            title: "Service Availment",
            items: [
                {
                    question: "How long does the approval process take?",
                    answer: "After your on-site interview and document submission, the approval process typically takes 24 hours."
                },
                {
                    question: "What happens after I receive assistance?",
                    answer: "After claiming assistance, you will enter a 12-month cooldown period for that specific service. During this time, you cannot apply for the same service again, but you may apply for other services one at a time."
                },
                {
                    question: "Can I apply for a different service while waiting for approval?",
                    answer: "No, you must wait for your current service application to be completed or withdrawn before applying for any other service. Only one service application can be active at a time."
                }
            ]
        }
        // ... other categories if any ...
    ];

    const categories = [
        {
            name: "Account & Registration",
            icon: <UserIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "How do I register for a Balagtas SocialCare account?",
                    answer: "To register, click on the 'Register' button on the top right of our website. You'll need to provide your full name, date of birth, sex at birth, full address in Balagtas, email, mobile number, and create a secure password. After registration, you'll need to verify your email and complete the residency verification process."
                },
                {
                    question: "What documents can I use to verify my residency?",
                    answer: "For residency verification, you can upload any of the following: latest electricity bill, water bill, or a government-issued ID with your Balagtas address. These documents will be manually verified by a social worker."
                },
                {
                    question: "What happens after I submit my residency proof?",
                    answer: "After submitting your proof of residency, a social worker will manually verify your documents. This typically takes 1-3 business days. You'll receive a notification when your account status changes from 'Pending Verification' to 'Verified Resident'."
                },
                {
                    question: "Can I apply for services immediately after registering?",
                    answer: "No, you need to complete two steps first: 1) Verify your residency by uploading proof documents, and 2) Complete the BFACES (Balagtas Financial Assistance for Crisis and Emergency Situations) application. Once both are verified, you'll gain full access to service applications."
                }
            ]
        },
        {
            name: "BFACES Application",
            icon: <FileTextIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "What is BFACES and why do I need to apply for it?",
                    answer: "BFACES stands for Balagtas Financial Assistance for Crisis and Emergency Situations. It's a formal application for emergency/crisis assistance and serves as a prerequisite to accessing specific social welfare services. Completing this form verifies your eligibility for assistance."
                },
                {
                    question: "What information do I need to provide in the BFACES application?",
                    answer: "The BFACES form requires information about your household income, details about the emergency or crisis situation you're facing, the number of family members in your household, and relevant supporting documents. Some information will be auto-filled from your registration details."
                },
                {
                    question: "How long does BFACES verification take?",
                    answer: "BFACES verification is typically completed within 3-5 business days. A social worker will review your submission to confirm your eligibility. You'll receive a notification when your application status changes to 'Verified'."
                },
                {
                    question: "Does my BFACES approval expire?",
                    answer: "Yes, BFACES verification is valid for 12 months. After this period, you'll need to renew your BFACES application to continue accessing services. You'll receive a notification when your BFACES is nearing expiration."
                }
            ]
        },
        {
            name: "Services & Applications",
            icon: <HeartIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "What specific services can I apply for?",
                    answer: "Once your BFACES is verified, you can apply for: Medical Assistance (for healthcare expenses), Burial Assistance (for funeral costs), Aid to Senior Citizens (for elderly residents), PAO Certification (for legal aid), and PhilHealth Certification. Each service has specific eligibility requirements and documentation needs."
                },
                {
                    question: "What is the cooldown period for services?",
                    answer: "After claiming assistance for a specific service, you will be placed under a 12-month cooldown period for that particular service. This means you cannot avail of the same service again during this period. This policy ensures equitable distribution of resources among Balagtas residents."
                },
                {
                    question: "Can I apply for multiple services simultaneously?",
                    answer: "No, you cannot apply for multiple services simultaneously. You can only have one active service application at a time. You must complete or withdraw your current service application before applying for another service. This ensures fair and efficient processing of all applications."
                },
                {
                    question: "What happens if my service application is rejected?",
                    answer: "If your application is rejected, you'll receive a notification explaining the reason. You can review the feedback, address the issues, and resubmit your application if applicable. For further assistance, you can message a social worker through the platform or visit the Municipal Social Welfare office."
                }
            ]
        },
        {
            name: "Document Requirements",
            icon: <ShieldCheckIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "What documents are required for Medical Assistance?",
                    answer: "For Medical Assistance, you need to provide: Valid ID, Medical Certificate (dated within 3 months), Hospital Bill or Quotation, and a Barangay Certificate of Indigency. These documents verify your identity, medical need, and financial situation."
                },
                {
                    question: "What documents are required for Burial Assistance?",
                    answer: "For Burial Assistance, you need: Valid ID, Death Certificate, Funeral Parlor Bill/Quotation, Barangay Certificate of Indigency, and Proof of Relationship to the Deceased (such as a birth certificate or marriage certificate)."
                },
                {
                    question: "Do I need to submit physical copies of my documents?",
                    answer: "Yes, the process has two stages: First, you upload digital copies (scans or photos) of your documents for initial verification. Once approved, you'll need to submit the physical (hardcopy) requirements and complete an on-site interview on the same day."
                },
                {
                    question: "How long are my uploaded documents stored in the system?",
                    answer: "Your documents are securely stored in our system throughout the application process and for a period afterward for auditing purposes. However, we maintain strict data privacy practices in compliance with Philippine data protection laws."
                }
            ]
        },
        {
            name: "Interviews & Appointments",
            icon: <ClockIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "When do I need to attend an interview?",
                    answer: "After your uploaded documents are verified, you'll be prompted to submit physical (hardcopy) documents and attend an on-site interview. This interview is scheduled on the same day you submit your physical documents."
                },
                {
                    question: "How long does the interview process take?",
                    answer: "The interview typically takes 15-30 minutes, depending on the service you're applying for and your specific situation. Bring all required original documents to expedite the process."
                },
                {
                    question: "What happens during the interview?",
                    answer: "During the interview, a social worker will verify your identity, review your documents, ask questions about your situation to assess your needs, and explain the next steps in the process. It's an opportunity to clarify any details about your application."
                },
                {
                    question: "Can I reschedule my interview if I can't make it?",
                    answer: "Yes, you can reschedule your interview through the 'Appointments' section on your dashboard. However, please do so at least 24 hours in advance whenever possible. Note that rescheduling may delay your application processing."
                }
            ]
        },
        {
            name: "Claiming Assistance",
            icon: <HandHeartIcon className="h-6 w-6 text-purple-600" />,
            faqs: [
                {
                    question: "How long after my interview will my application be processed?",
                    answer: "Within 24 hours after your interview, the social worker will validate your hardcopy documents and your system status will be updated to 'Approved for Claiming' if everything is in order. You'll receive a notification via dashboard and email."
                },
                {
                    question: "How do I claim approved assistance?",
                    answer: "Once your application is approved for claiming, you'll need to visit the Municipal Social Welfare office with your valid ID. The notification will include specific instructions on where and when to claim your assistance."
                },
                {
                    question: "Can someone claim the assistance on my behalf?",
                    answer: "In special circumstances (such as medical confinement), a representative may claim on your behalf. They must bring an authorization letter, your valid ID (photocopy), their own valid ID, and any other specified documents. This must be pre-approved by contacting the social welfare office."
                },
                {
                    question: "What happens if I don't claim my assistance within the given timeframe?",
                    answer: "Approved assistance should be claimed within 30 days of approval. If you fail to claim within this period, your application may be cancelled, and you'll need to reapply. Extensions may be granted in exceptional circumstances by contacting the office."
                }
            ]
        }
    ];

    return (
        <LandingLayout title="Frequently Asked Questions">
            <Head title="FAQs - Balagtas SocialCare" />
            
            <div className="container mx-auto px-4 py-12">
                {/* Introduction */}
                <div className="max-w-3xl mx-auto mb-16 text-center">
                    {isLoading ? (
                        <>
                            <Skeleton height={40} width={300} className="mb-4" />
                            <Skeleton count={2} />
                        </>
                    ) : (
                        <>
                            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                                <HelpCircleIcon className="h-4 w-4" />
                                Help & Support Center
                            </div>
                            <h1 className="text-4xl md:text-5xl font-bold text-purple-900 mb-6 leading-tight">Frequently Asked Questions</h1>
                            <p className="text-xl text-gray-600 leading-relaxed mb-8">
                                Find answers to common questions about Balagtas SocialCare services,
                                application processes, and requirements. Can't find what you're looking for? Contact our support team.
                            </p>
                            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span>Instant Answers</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span>Step-by-Step Guides</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                                    <span>Expert Support</span>
                                </div>
                            </div>
                        </>
                    )}
                </div>

                <div className="grid lg:grid-cols-5 gap-8">
                    <div className="lg:col-span-1">
                        <div className="bg-purple-50 p-4 rounded-lg sticky top-24">
                            <h3 className="font-semibold text-purple-800 mb-4">Categories</h3>
                            <ul className="space-y-2">
                                {categories.map((category, index) => (
                                    <li key={index}>
                                        <a 
                                            href={`#${category.name.toLowerCase().replace(/\s+/g, '-')}`} 
                                            className="flex items-center gap-2 text-gray-700 hover:text-purple-700 py-1"
                                        >
                                            {category.icon}
                                            <span>{category.name}</span>
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                    
                    <div className="lg:col-span-4">
                        {isLoading ? (
                            Array(4).fill(null).map((_, index) => (
                                <FAQCategorySkeleton key={index} />
                            ))
                        ) : (
                            categories.map((category, index) => (
                                <div 
                                    key={index} 
                                    id={category.name.toLowerCase().replace(/\s+/g, '-')}
                                    className="mb-12"
                                >
                                    <div className="flex items-center gap-3 mb-6">
                                        <div className="bg-purple-100 p-2 rounded-full">
                                            {category.icon}
                                        </div>
                                        <h2 className="text-2xl font-bold text-gray-900">{category.name}</h2>
                                    </div>
                                    
                                    <Card>
                                        <CardContent className="pt-6">
                                            <Accordion type="multiple" className="w-full">
                                                {category.faqs.map((faq, idx) => {
                                                    const itemId = `item-${index}-${idx}`;
                                                    return (
                                                        <AccordionItem key={idx} value={itemId}>
                                                            <AccordionTrigger 
                                                                className="text-left font-medium text-gray-800"
                                                                onClick={() => toggleItem(itemId)}
                                                                open={openItems[itemId]}
                                                            >
                                                                {faq.question}
                                                            </AccordionTrigger>
                                                            <AccordionContent 
                                                                className="text-gray-600"
                                                                open={openItems[itemId]}
                                                            >
                                                                {faq.answer}
                                                            </AccordionContent>
                                                        </AccordionItem>
                                                    );
                                                })}
                                            </Accordion>
                                        </CardContent>
                                    </Card>
                                </div>
                            ))
                        )}
                    </div>
                </div>

                <div className="max-w-4xl mx-auto mt-20 bg-purple-50 p-8 rounded-xl">
                    <h2 className="text-2xl font-bold mb-4">Still Have Questions?</h2>
                    <p className="mb-6">
                        If you couldn't find the answer to your question, please don't hesitate to contact us. 
                        Our team is ready to assist you.
                    </p>
                    <div className="grid md:grid-cols-2 gap-4">
                        <div className="bg-purple-700 p-4 rounded-lg text-white">
                            <h3 className="font-semibold mb-2">Visit Us</h3>
                            <p className="text-purple-100 text-sm">
                                Municipal Social Welfare and Development Office<br />
                                Balagtas Municipal Hall<br />
                                Balagtas, Bulacan
                            </p>
                            <p className="text-purple-100 text-sm mt-2">
                                Office Hours: Monday - Friday, 8:00 AM - 5:00 PM
                            </p>
                        </div>
                        <div className="bg-purple-700 p-4 rounded-lg text-white">
                            <h3 className="font-semibold mb-2">Contact Us</h3>
                            <p className="text-purple-100 text-sm">
                                Phone: (*************<br />
                                Email: <EMAIL>
                            </p>
                            <p className="text-purple-100 text-sm mt-2">
                                For emergencies outside office hours,<br />
                                please call our 24/7 hotline: (*************
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </LandingLayout>
    );
} 