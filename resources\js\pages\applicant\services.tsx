import { <PERSON>, <PERSON> } from "@inertiajs/react"
import AppLayout from "@/layouts/app/app-sidebar-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { CalendarDays, Clock, FileText, Users } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { dummyServices, dummyUserApplications } from "@/data/dummy-data"

interface Service {
  id: number
  title: string
  description: string
  eligibility: string[]
  requirements: string[]
  process: string[]
  status: "available" | "limited" | "unavailable"
  schedule: string
  location: string
  maxBeneficiaries?: number
  currentBeneficiaries?: number
}

interface Props {
  services: Service[]
  userApplications: {
    serviceId: number
    status: "pending" | "approved" | "rejected"
    appliedAt: string
    rejectionReason?: string | null
  }[]
}

export default function Services({ services = dummyServices, userApplications = dummyUserApplications }: Props) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/client/dashboard" },
    { title: "Social Services", href: "/client/services" },
  ]

  function getStatusColor(status: Service["status"]) {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800"
      case "limited":
        return "bg-yellow-100 text-yellow-800"
      case "unavailable":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  function getApplicationStatus(serviceId: number) {
    return userApplications.find(app => app.serviceId === serviceId)
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Social Services" />

      <div className="flex flex-col gap-6">
        <Heading 
          title="Social Services" 
          description="Browse and apply for available social services in Balagtas." 
        />

        <div className="grid gap-6 md:grid-cols-2">
          {services.map(service => {
            const application = getApplicationStatus(service.id)
            
            return (
              <Card key={service.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>{service.title}</CardTitle>
                      <CardDescription className="mt-2">
                        {service.description}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusColor(service.status)}>
                      {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Schedule and Location */}
                    <div className="flex flex-col gap-3">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <CalendarDays className="mr-2 h-4 w-4" />
                        {service.schedule}
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="mr-2 h-4 w-4" />
                        {service.location}
                      </div>
                      {service.maxBeneficiaries && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Users className="mr-2 h-4 w-4" />
                          {service.currentBeneficiaries} / {service.maxBeneficiaries} beneficiaries
                        </div>
                      )}
                    </div>

                    {/* Eligibility */}
                    <div>
                      <h4 className="mb-2 text-sm font-medium">Eligibility</h4>
                      <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                        {service.eligibility.map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Requirements */}
                    <div>
                      <h4 className="mb-2 text-sm font-medium">Requirements</h4>
                      <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                        {service.requirements.map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Process */}
                    <div>
                      <h4 className="mb-2 text-sm font-medium">Application Process</h4>
                      <ol className="list-inside list-decimal space-y-1 text-sm text-muted-foreground">
                        {service.process.map((step, index) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ol>
                    </div>

                    {/* Application Status or Apply Button */}
                    <div className="flex items-center justify-between border-t pt-4">
                      {application ? (
                        <div className="flex items-center gap-2">
                          <Badge
                            className={
                              application.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : application.status === "rejected"
                                ? "bg-red-100 text-red-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            Applied on {new Date(application.appliedAt).toLocaleDateString()}
                          </span>
                        </div>
                      ) : (
                        <Link
                          href={`/client/services/${service.id}/apply`}
                          className={`inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 ${
                            service.status === "unavailable" ? "pointer-events-none opacity-50" : ""
                          }`}
                        >
                          <FileText className="mr-2 h-4 w-4" />
                          Apply Now
                        </Link>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </AppLayout>
  )
} 