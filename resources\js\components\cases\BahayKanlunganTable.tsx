import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';

interface BahayKanlunganCase {
    id: number;
    caseloadNo: string;
    dateOfAdmission: string;
    nameOfCCL: string;
    address: string;
    birthdate: string;
    sex: string;
    age: number;
    nameOfGuardian: string;
    addressOfGuardian: string;
    date: string;
    highest: string;
    ageAtTheTimeOf: string;
    offense: string;
    statusOfTheCase: string;
    activeInactive: string;
}

interface BahayKanlunganTableProps {
    cases: BahayKanlunganCase[];
    onEdit?: (caseItem: BahayKanlunganCase) => void;
    onDelete?: (caseItem: BahayKanlunganCase) => void;
    onView?: (caseItem: BahayKanlunganCase) => void;
}

export default function BahayKanlunganTable({ cases, onEdit, onDelete, onView }: BahayKanlunganTableProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const filteredCases = cases.filter(caseItem =>
        Object.values(caseItem).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;
        
        const aValue = a[sortField as keyof BahayKanlunganCase];
        const bValue = b[sortField as keyof BahayKanlunganCase];
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return (
        <div className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search Bahay Kanlungan cases..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            {/* Table */}
            <div className="border border-gray-800 rounded-lg overflow-hidden">
                <div className="bg-red-600 text-white text-center py-2 font-bold text-sm">
                    MASTERLIST CCL<br />
                    MUNICIPALITY OF BALAGTAS<br />
                    2024
                </div>
                
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-100 border-b border-gray-800">
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-bold text-xs">
                                    Caseload no.
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">DATE OF ADMISSION</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">NAME OF CCL</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">ADDRESS</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Birthdate</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">SEX</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">AGE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">NAME OF GUARDIAN</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">ADDRESS OF GUARDIAN</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">DATE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">HIGHEST</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">AGE AT THE TIME OF</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">OFFENSE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">STATUS OF THE CASE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">ACTIVE/INACTIVE</TableHead>
                            <TableHead className="text-center font-bold text-xs p-2">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedCases.map((caseItem) => (
                            <TableRow key={caseItem.id} className="hover:bg-gray-50 border-b border-gray-800">
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.caseloadNo}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.dateOfAdmission}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.nameOfCCL}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.address}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.birthdate}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.sex}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.age}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.nameOfGuardian}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.addressOfGuardian}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.date}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.highest}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.ageAtTheTimeOf}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.offense}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.statusOfTheCase}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.activeInactive}</TableCell>
                                <TableCell className="text-center p-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onView?.(caseItem)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                View
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onEdit?.(caseItem)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onDelete?.(caseItem)} className="text-red-600">
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
