<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove any existing family member users (keep only head_of_family)
        DB::table('users')->where('family_role', 'family_member')->delete();

        // Update the family_role enum to only allow head_of_family
        DB::statement("ALTER TABLE users MODIFY COLUMN family_role ENUM('head_of_family') DEFAULT 'head_of_family'");

        // Remove head_of_family_id and family_relationship columns as they're no longer needed
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['head_of_family_id']);
            $table->dropColumn(['head_of_family_id', 'family_relationship']);
        });

        // Update all existing users to be head_of_family
        DB::table('users')->whereNull('family_role')->update(['family_role' => 'head_of_family']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore the original family_role enum
        DB::statement("ALTER TABLE users MODIFY COLUMN family_role ENUM('head_of_family', 'family_member') DEFAULT NULL");

        // Add back the removed columns
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('head_of_family_id')->nullable()->constrained('users')->onDelete('cascade')->after('bfaces_control_code');
            $table->enum('family_relationship', [
                'spouse', 'child', 'parent', 'sibling', 'grandparent',
                'grandchild', 'aunt', 'uncle', 'cousin', 'other'
            ])->nullable()->after('head_of_family_id');
        });
    }
};
