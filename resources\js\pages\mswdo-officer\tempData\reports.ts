export const servicePerformance = [
    {
        month: 'January',
        totalApplications: 180,
        successRate: 88,
        averageProcessingDays: 4.8,
        satisfactionScore: 4.2
    },
    {
        month: 'February',
        totalApplications: 210,
        successRate: 92,
        averageProcessingDays: 4.5,
        satisfactionScore: 4.4
    },
    {
        month: 'March',
        totalApplications: 245,
        successRate: 90,
        averageProcessingDays: 4.2,
        satisfactionScore: 4.3
    },
    {
        month: 'April',
        totalApplications: 198,
        successRate: 87,
        averageProcessingDays: 4.9,
        satisfactionScore: 4.1
    },
    {
        month: 'May',
        totalApplications: 267,
        successRate: 91,
        averageProcessingDays: 4.4,
        satisfactionScore: 4.5
    },
    {
        month: 'June',
        totalApplications: 289,
        successRate: 93,
        averageProcessingDays: 4.1,
        satisfactionScore: 4.6
    }
];

export const serviceCategories = [
    {
        name: 'Medical Assistance',
        applications: 856,
        successRate: 92,
        averageProcessingTime: 4.2,
        satisfactionScore: 4.5
    },
    {
        name: 'Educational Support',
        applications: 634,
        successRate: 88,
        averageProcessingTime: 5.1,
        satisfactionScore: 4.3
    },
    {
        name: 'Housing Assistance',
        applications: 445,
        successRate: 76,
        averageProcessingTime: 6.3,
        satisfactionScore: 4.0
    },
    {
        name: 'Financial Aid',
        applications: 521,
        successRate: 82,
        averageProcessingTime: 5.5,
        satisfactionScore: 4.2
    }
];

export const workloadDistribution = {
    socialWorkers: [
        { name: 'SW001', activeApplications: 24, completedThisMonth: 45, averageProcessingTime: 4.2 },
        { name: 'SW002', activeApplications: 18, completedThisMonth: 38, averageProcessingTime: 4.5 },
        { name: 'SW003', activeApplications: 21, completedThisMonth: 42, averageProcessingTime: 4.1 },
        { name: 'SW004', activeApplications: 16, completedThisMonth: 35, averageProcessingTime: 4.8 }
    ],
    departments: [
        { name: 'Medical Services', activeApplications: 89, completedThisMonth: 156, staffCount: 4 },
        { name: 'Education', activeApplications: 67, completedThisMonth: 124, staffCount: 3 },
        { name: 'Housing', activeApplications: 45, completedThisMonth: 98, staffCount: 2 },
        { name: 'Financial', activeApplications: 56, completedThisMonth: 112, staffCount: 3 }
    ]
};

export const barangayDistribution = [
  { barangay: "Borol 1st", applications: 120, approved: 100, total_amount: 500000 },
  { barangay: "Borol 2nd", applications: 90, approved: 82, total_amount: 410000 },
  { barangay: "Dalig", applications: 150, approved: 140, total_amount: 700000 },
  { barangay: "Longos", applications: 80, approved: 75, total_amount: 375000 },
  { barangay: "Panginay", applications: 110, approved: 98, total_amount: 490000 },
  { barangay: "Pulong Gubat", applications: 100, approved: 90, total_amount: 450000 },
  { barangay: "San Juan", applications: 130, approved: 120, total_amount: 600000 },
  { barangay: "Santol", applications: 70, approved: 65, total_amount: 325000 },
  { barangay: "Wawa", applications: 85, approved: 80, total_amount: 400000 }
];

export const monthlyTrends = [
  { month: "Jan", applications: 80, approved: 72, amount: 360000 },
  { month: "Feb", applications: 95, approved: 88, amount: 440000 },
  { month: "Mar", applications: 120, approved: 110, amount: 550000 },
  { month: "Apr", applications: 100, approved: 95, amount: 475000 }
];

export const stats = {
  totalApplications: 935,
  totalApproved: 850,
  totalRejected: 45,
  totalPending: 40,
  averageProcessingDays: 3.1,
  totalAmountDisbursed: 4250000,
  overallSatisfactionRate: 95,
  applicationTrend: "increasing",
  mostRequestedService: "School Supplies Assistance",
  mostActiveBarangay: "Dalig"
};

export const staffPerformance = [
  {
    id: 1,
    name: "Juan Dela Cruz",
    applications_processed: 120,
    average_processing_time: 2.5,
    approval_rate: 95,
    client_satisfaction: 98
  },
  {
    id: 2,
    name: "Maria Santos",
    applications_processed: 95,
    average_processing_time: 3.2,
    approval_rate: 92,
    client_satisfaction: 95
  },
  {
    id: 3,
    name: "Pedro Penduko",
    applications_processed: 85,
    average_processing_time: 3.8,
    approval_rate: 88,
    client_satisfaction: 90
  }
];

export const auditLogs = [
  {
    id: 1,
    type: "system_config",
    action: "update",
    component: "email_settings",
    user: "Juan Dela Cruz",
    details: {
      previous: { smtp_host: "smtp1.example.com" },
      current: { smtp_host: "smtp2.example.com" }
    },
    timestamp: "2024-04-09T08:30:00.000Z",
    ip_address: "*************"
  },
  {
    id: 2,
    type: "service_config",
    action: "create",
    component: "medical_assistance",
    user: "Maria Santos",
    details: {
      service_name: "Hospital Bill Assistance",
      max_amount: 10000
    },
    timestamp: "2024-04-09T09:15:00.000Z",
    ip_address: "*************"
  }
];

export const securityIncidents = [
  {
    id: 1,
    type: "unauthorized_access",
    severity: "high",
    status: "resolved",
    description: "Multiple failed login attempts detected",
    affected_user: "<EMAIL>",
    ip_address: "***********",
    timestamp: "2024-03-15T10:30:00.000Z",
    resolution: "Account temporarily locked, password reset required",
    resolution_time: 30 // minutes
  },
  {
    id: 2,
    type: "suspicious_activity",
    severity: "medium",
    status: "investigating",
    description: "Unusual number of application submissions",
    affected_user: "<EMAIL>",
    ip_address: "***********",
    timestamp: "2024-04-01T14:20:00.000Z",
    resolution: null,
    resolution_time: null
  }
];

export const complianceReports = [
  {
    id: 1,
    type: "data_privacy",
    status: "compliant",
    last_audit: "2024-03-01T00:00:00.000Z",
    next_audit: "2024-06-01T00:00:00.000Z",
    findings: [],
    recommendations: [
      "Implement additional encryption layers",
      "Update privacy policy documentation"
    ]
  },
  {
    id: 2,
    type: "security",
    status: "action_required",
    last_audit: "2024-02-15T00:00:00.000Z",
    next_audit: "2024-05-15T00:00:00.000Z",
    findings: [
      "Outdated SSL certificates",
      "Weak password policies"
    ],
    recommendations: [
      "Renew SSL certificates",
      "Enforce stronger password requirements"
    ]
  }
]; 