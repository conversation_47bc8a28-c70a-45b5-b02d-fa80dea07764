<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // BFACES family role field (only head_of_family allowed)
            $table->enum('family_role', ['head_of_family'])->default('head_of_family')->after('role');

            // BFACES control code for linking family members to head of family
            $table->string('bfaces_control_code', 12)->nullable()->unique()->after('family_role');

            // Reference to head of family for family members
            $table->foreignId('head_of_family_id')->nullable()->constrained('users')->onDelete('cascade')->after('bfaces_control_code');

            // Family relationship type for family members
            $table->enum('family_relationship', [
                'spouse', 'child', 'parent', 'sibling', 'grandparent',
                'grandchild', 'aunt', 'uncle', 'cousin', 'other'
            ])->nullable()->after('head_of_family_id');

            // Document verification status for BFACES
            $table->enum('document_verification_status', [
                'pending', 'under_review', 'approved', 'rejected', 'requires_additional_docs'
            ])->default('pending')->after('family_relationship');

            // BFACES form completion status
            $table->enum('bfaces_status', [
                'not_started', 'documents_pending', 'documents_submitted',
                'under_review', 'form_accessible', 'form_completed', 'approved'
            ])->default('not_started')->after('document_verification_status');

            // Timestamps for BFACES process tracking
            $table->timestamp('documents_submitted_at')->nullable()->after('bfaces_status');
            $table->timestamp('documents_verified_at')->nullable()->after('documents_submitted_at');
            $table->timestamp('bfaces_form_completed_at')->nullable()->after('documents_verified_at');
            $table->timestamp('bfaces_approved_at')->nullable()->after('bfaces_form_completed_at');

            // Social worker who verified the documents
            $table->foreignId('verified_by')->nullable()->constrained('users')->after('bfaces_approved_at');

            // Notes from social worker during verification
            $table->text('verification_notes')->nullable()->after('verified_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['head_of_family_id']);
            $table->dropForeign(['verified_by']);
            $table->dropColumn([
                'family_role',
                'bfaces_control_code',
                'head_of_family_id',
                'family_relationship',
                'document_verification_status',
                'bfaces_status',
                'documents_submitted_at',
                'documents_verified_at',
                'bfaces_form_completed_at',
                'bfaces_approved_at',
                'verified_by',
                'verification_notes'
            ]);
        });
    }
};
