import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';

interface ReformistCase {
    id: number;
    caseloadNo: string;
    date: string;
    names: string;
    birthdate: string;
    age: number;
    address: string;
    contactNumber: string;
    typeOfViolation: string;
    dateReported: string;
    noOfMonthsForCounselling: number;
    dateFinishedCounselling: string;
    remarks: string;
}

interface ReformistTableProps {
    cases: ReformistCase[];
    onEdit?: (caseItem: ReformistCase) => void;
    onDelete?: (caseItem: ReformistCase) => void;
    onView?: (caseItem: ReformistCase) => void;
}

export default function ReformistTable({ cases, onEdit, onDelete, onView }: ReformistTableProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const filteredCases = cases.filter(caseItem =>
        Object.values(caseItem).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;
        
        const aValue = a[sortField as keyof ReformistCase];
        const bValue = b[sortField as keyof ReformistCase];
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return (
        <div className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search reformist cases..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            {/* Table */}
            <div className="border border-gray-800 rounded-lg overflow-hidden">
                <div className="bg-red-600 text-white text-center py-2 font-bold text-sm">
                    LIST OF REFORMIST<br />
                    MUNICIPALITY OF BALAGTAS
                </div>
                
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-100 border-b border-gray-800">
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-bold text-xs">
                                    Caseload No.
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('date')} className="h-auto p-0 font-bold text-xs">
                                    Date
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('names')} className="h-auto p-0 font-bold text-xs">
                                    NAMES
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Birthdate</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Age</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Address</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Contact Number</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">TYPE OF VIOLATION</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Date Reported</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">NO. OF MONTHS FOR COUNSELLING</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">DATE FINISHED COUNSELLING</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">REMARKS</TableHead>
                            <TableHead className="text-center font-bold text-xs p-2">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedCases.map((caseItem) => (
                            <TableRow key={caseItem.id} className="hover:bg-gray-50 border-b border-gray-800">
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.caseloadNo}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.date}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.names}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.birthdate}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.age}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.address}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.contactNumber}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.typeOfViolation}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.dateReported}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.noOfMonthsForCounselling}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.dateFinishedCounselling || '-'}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.remarks}</TableCell>
                                <TableCell className="text-center p-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onView?.(caseItem)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                View
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onEdit?.(caseItem)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onDelete?.(caseItem)} className="text-red-600">
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
