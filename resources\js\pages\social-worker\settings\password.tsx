import { Head } from "@inertiajs/react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { useForm } from "@inertiajs/react"
import SettingsLayout from "@/layouts/settings-layout"
import { KeyRound, Lock, ShieldCheck } from "lucide-react"
import { type BreadcrumbItem } from "@/types"

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
  },
  {
    title: "Settings",
    href: "/admin/settings/password",
  },
  {
    title: "Password",
    href: "/admin/settings/password",
  },
]

export default function AdminPassword() {
  const { data, setData, patch, errors, processing, reset } = useForm({
    current_password: "",
    password: "",
    password_confirmation: "",
  })

  function submit(e: React.FormEvent) {
    e.preventDefault()
    patch(`/admin/settings/password`, {
      onSuccess: () => reset("password", "password_confirmation"),
    })
  }

  return (
    <SettingsLayout breadcrumbs={breadcrumbs} role="admin" current="password">
      <Head title="Change Password" />

      <div className="space-y-6">
        <Card>
          <form onSubmit={submit}>
            <CardHeader>
              <div className="flex items-center gap-4 mb-4">
                <div className="h-16 w-16 rounded-full bg-purple-100 flex items-center justify-center">
                  <KeyRound className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <CardTitle>Change Password</CardTitle>
                  <CardDescription>
                    Ensure your account is using a secure password.
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6">
                <div>
                  <Label htmlFor="current_password" className="text-purple-900 mb-1.5 block">
                    Current Password
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-purple-400" />
                    </div>
                    <Input
                      id="current_password"
                      type="password"
                      value={data.current_password}
                      onChange={e => setData("current_password", e.target.value)}
                      className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                      placeholder="Enter your current password"
                    />
                  </div>
                  {errors.current_password && (
                    <p className="text-sm text-red-500 mt-1">{errors.current_password}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="password" className="text-purple-900 mb-1.5 block">
                    New Password
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <ShieldCheck className="h-5 w-5 text-purple-400" />
                    </div>
                    <Input
                      id="password"
                      type="password"
                      value={data.password}
                      onChange={e => setData("password", e.target.value)}
                      className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                      placeholder="Enter your new password"
                    />
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-500 mt-1">{errors.password}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="password_confirmation" className="text-purple-900 mb-1.5 block">
                    Confirm New Password
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <ShieldCheck className="h-5 w-5 text-purple-400" />
                    </div>
                    <Input
                      id="password_confirmation"
                      type="password"
                      value={data.password_confirmation}
                      onChange={e => setData("password_confirmation", e.target.value)}
                      className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                      placeholder="Confirm your new password"
                    />
                  </div>
                  {errors.password_confirmation && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.password_confirmation}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={processing}
                  className="bg-purple-600 text-white hover:bg-purple-700"
                >
                  Update Password
                </Button>
              </div>
            </CardContent>
          </form>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 rounded-full bg-amber-100 flex items-center justify-center">
                <Lock className="h-8 w-8 text-amber-600" />
              </div>
              <div>
                <CardTitle>Password Requirements</CardTitle>
                <CardDescription>
                  Make sure your new password meets these requirements.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2 text-green-700">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>Minimum 8 characters long</span>
              </li>
              <li className="flex items-center gap-2 text-green-700">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>At least one uppercase letter</span>
              </li>
              <li className="flex items-center gap-2 text-green-700">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>At least one lowercase letter</span>
              </li>
              <li className="flex items-center gap-2 text-green-700">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>At least one number</span>
              </li>
              <li className="flex items-center gap-2 text-green-700">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span>At least one special character</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </SettingsLayout>
  )
} 