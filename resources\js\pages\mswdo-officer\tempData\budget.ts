export const budgetAllocations = [
  {
    id: 1,
    category: "Medical Assistance",
    annual_budget: 5000000,
    allocated: 2500000,
    remaining: 2500000,
    monthly_allocation: 416666.67,
    beneficiaries_served: 250,
    average_assistance: 10000,
    status: "on_track"
  },
  {
    id: 2,
    category: "Educational Support",
    annual_budget: 3000000,
    allocated: 1800000,
    remaining: 1200000,
    monthly_allocation: 250000,
    beneficiaries_served: 600,
    average_assistance: 3000,
    status: "warning"
  },
  {
    id: 3,
    category: "Financial Aid",
    annual_budget: 2000000,
    allocated: 900000,
    remaining: 1100000,
    monthly_allocation: 166666.67,
    beneficiaries_served: 180,
    average_assistance: 5000,
    status: "on_track"
  },
  {
    id: 4,
    category: "Emergency Relief",
    annual_budget: 1500000,
    allocated: 1400000,
    remaining: 100000,
    monthly_allocation: 125000,
    beneficiaries_served: 280,
    average_assistance: 5000,
    status: "critical"
  }
];

export const stats = {
  totalBudget: 11500000,
  totalAllocated: 6600000,
  totalRemaining: 4900000,
  totalBeneficiaries: 1310,
  monthlyAverage: 550000,
  utilizationRate: 57.39,
  projectedYearEnd: {
    surplus: 2000000,
    deficit: 0
  }
};

export const monthlyData = [
  { month: "Jan", allocated: 550000, used: 520000 },
  { month: "Feb", allocated: 550000, used: 535000 },
  { month: "Mar", allocated: 550000, used: 548000 },
  { month: "Apr", allocated: 550000, used: 542000 },
  { month: "May", allocated: 550000, used: 0 },
  { month: "Jun", allocated: 550000, used: 0 },
  { month: "Jul", allocated: 550000, used: 0 },
  { month: "Aug", allocated: 550000, used: 0 },
  { month: "Sep", allocated: 550000, used: 0 },
  { month: "Oct", allocated: 550000, used: 0 },
  { month: "Nov", allocated: 550000, used: 0 },
  { month: "Dec", allocated: 550000, used: 0 }
];

export const emergencyFunds = {
  total: 2000000,
  allocated: 500000,
  remaining: 1500000,
  lastReplenished: "2024-01-01T00:00:00.000Z",
  disbursements: [
    {
      id: 1,
      amount: 200000,
      purpose: "Flood Relief",
      date: "2024-02-15T00:00:00.000Z",
      beneficiaries: 40,
      status: "completed"
    },
    {
      id: 2,
      amount: 300000,
      purpose: "Fire Victims Support",
      date: "2024-03-20T00:00:00.000Z",
      beneficiaries: 60,
      status: "completed"
    }
  ],
  minimumRequired: 1000000,
  replenishmentThreshold: 1200000
};

export const riskAssessment = {
  budgetRisks: [
    {
      id: 1,
      category: "Medical Assistance",
      risk_level: "high",
      factors: [
        "Increasing healthcare costs",
        "Growing elderly population",
        "Rising chronic conditions"
      ],
      mitigation: "Increase emergency fund allocation by 20%",
      impact_score: 8,
      probability_score: 7
    },
    {
      id: 2,
      category: "Educational Support",
      risk_level: "medium",
      factors: [
        "Seasonal demand spikes",
        "Limited school supplies vendors"
      ],
      mitigation: "Early procurement and stockpiling",
      impact_score: 6,
      probability_score: 5
    }
  ],
  systemicRisks: [
    {
      id: 1,
      type: "Natural Disasters",
      probability: "medium",
      impact: "high",
      preparedness_level: 7,
      required_reserve: 1000000
    },
    {
      id: 2,
      type: "Economic Downturn",
      probability: "low",
      impact: "high",
      preparedness_level: 6,
      required_reserve: 800000
    }
  ],
  recommendations: [
    {
      id: 1,
      title: "Increase Emergency Fund",
      description: "Maintain higher emergency fund due to increased risk factors",
      priority: "high",
      implementation_timeline: "Q3 2024",
      estimated_cost: 500000
    },
    {
      id: 2,
      title: "Diversify Service Providers",
      description: "Partner with more service providers to reduce dependency risks",
      priority: "medium",
      implementation_timeline: "Q4 2024",
      estimated_cost: 100000
    }
  ]
}; 