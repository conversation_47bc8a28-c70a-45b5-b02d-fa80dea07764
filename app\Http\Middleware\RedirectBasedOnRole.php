<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            return redirect('/login');
        }

        $user = Auth::user();
        $intendedUrl = $request->session()->get('url.intended');
        $currentPath = $request->path();
        
        // Debug info
        \Log::info('User accessing path', [
            'user' => $user->name,
            'role' => $user->role,
            'path' => $currentPath
        ]);

        // Social Worker routes should only be accessible by social worker users
        if (strpos($currentPath, 'social-worker/') === 0 && $user->role !== 'social-worker') {
            \Log::warning('Non-social-worker trying to access social worker route', [
                'user' => $user->name,
                'role' => $user->role,
                'path' => $currentPath
            ]);

            // Redirect based on user's role
            switch ($user->role) {
                case 'mswdo-officer':
                    return redirect(route('mswdo-officer.dashboard'));
                case 'applicant':
                    return redirect(route('applicant.dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        // MSWDO Officer routes should only be accessible by MSWDO Officer users
        if (strpos($currentPath, 'mswdo-officer/') === 0 && $user->role !== 'mswdo-officer') {
            \Log::warning('Non-mswdo-officer trying to access mswdo-officer route', [
                'user' => $user->name,
                'role' => $user->role,
                'path' => $currentPath
            ]);

            // Redirect based on user's role
            switch ($user->role) {
                case 'social-worker':
                    return redirect(route('social-worker.dashboard'));
                case 'applicant':
                    return redirect(route('applicant.dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        // Applicant routes should be accessible by all authenticated users
        // but social-worker and mswdo-officer users should be redirected to their respective dashboards
        // if they try to access the applicant dashboard
        if ($currentPath === 'applicant/dashboard' && $user->role !== 'applicant') {
            switch ($user->role) {
                case 'mswdo-officer':
                    return redirect(route('mswdo-officer.dashboard'));
                case 'social-worker':
                    return redirect(route('social-worker.dashboard'));
                default:
                    // Allow access
                    break;
            }
        }

        // If there's no intended URL or the intended URL is the home page
        if (!$intendedUrl || $intendedUrl === '/' || $intendedUrl === route('home')) {
            switch ($user->role) {
                case 'mswdo-officer':
                    return redirect(route('mswdo-officer.dashboard'));
                case 'social-worker':
                    return redirect(route('social-worker.dashboard'));
                case 'applicant':
                    return redirect(route('applicant.dashboard'));
                default:
                    return redirect(route('login'));
            }
        }

        return $next($request);
    }
}
