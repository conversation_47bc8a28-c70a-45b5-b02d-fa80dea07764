import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { TrendingUp, Calculator, BarChart, Target, Calendar, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Budget and DSS',
        href: '#',
    },
    {
        title: 'Decision Support System',
        href: '/mswdo-officer/dss-report',
    },
];

export default function DecisionSupportSystem() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Decision Support System" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Decision Support System</h1>
                        <p className="text-purple-600 mt-2">Historical data analysis for annual budget proposal and barangay allocation planning</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Calculator className="h-4 w-4 mr-2" />
                            Analyze Historical Data
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Target className="h-4 w-4 mr-2" />
                            Generate Budget Proposal
                        </Button>
                    </div>
                </div>

                {/* Historical Analysis Summary */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">3-Year Average</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">₱2.1M</div>
                            <p className="text-xs text-purple-600">Annual disbursement</p>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Growth Trend</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">+12%</div>
                            <p className="text-xs text-green-600">Year-over-year increase</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Peak Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">December</div>
                            <p className="text-xs text-blue-600">Highest demand period</p>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Recommended Budget</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">₱2.4M</div>
                            <p className="text-xs text-orange-600">For 2025 proposal</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Budget Planning Tools */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card className="border-purple-200">
                        <CardHeader>
                            <CardTitle className="text-purple-900 flex items-center">
                                <Calendar className="h-5 w-5 mr-2" />
                                Annual Budget Proposal Generator
                            </CardTitle>
                            <CardDescription className="text-purple-600">
                                Generate data-driven budget proposal based on 3-year historical analysis
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="bg-purple-50 p-4 rounded-lg">
                                    <h4 className="font-medium text-purple-900 mb-2">Historical Analysis (2022-2024)</h4>
                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="text-purple-600">2022:</span>
                                            <span className="font-medium ml-2">₱1.8M</span>
                                        </div>
                                        <div>
                                            <span className="text-purple-600">2023:</span>
                                            <span className="font-medium ml-2">₱2.1M</span>
                                        </div>
                                        <div>
                                            <span className="text-purple-600">2024:</span>
                                            <span className="font-medium ml-2">₱2.4M</span>
                                        </div>
                                        <div>
                                            <span className="text-purple-600">Trend:</span>
                                            <span className="font-medium ml-2 text-green-600">+12% annually</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-orange-50 p-4 rounded-lg">
                                    <h4 className="font-medium text-orange-900 mb-2">2025 Recommendation</h4>
                                    <p className="text-sm text-orange-700">Based on trend analysis and seasonal patterns, recommend <strong>₱2.7M</strong> to avoid mid-year supplementary budget requests.</p>
                                </div>
                                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                                    Generate Proposal Letter
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900 flex items-center">
                                <MapPin className="h-5 w-5 mr-2" />
                                Barangay Budget Allocation
                            </CardTitle>
                            <CardDescription className="text-blue-600">
                                Analyze beneficiary distribution and suggest barangay-specific budget allocation
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <h4 className="font-medium text-blue-900 mb-2">Top Beneficiary Barangays</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Wawa</span>
                                            <span className="font-medium">18% (₱486K)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>San Juan</span>
                                            <span className="font-medium">15% (₱405K)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Panginay</span>
                                            <span className="font-medium">12% (₱324K)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Others (6 barangays)</span>
                                            <span className="font-medium">55% (₱1.485M)</span>
                                        </div>
                                    </div>
                                </div>
                                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                                    Generate Allocation Plan
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Historical Data Analysis Dashboard */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <BarChart className="h-5 w-5 mr-2" />
                            Historical Data Analysis Dashboard
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            3-year historical data analysis for informed budget planning decisions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {/* Monthly Breakdown Analysis */}
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <h4 className="font-medium text-gray-900 mb-3">Monthly Disbursement Patterns</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Peak Season (Nov-Jan):</span>
                                            <span className="font-medium text-red-600">35% of annual budget</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Regular Season (Feb-Oct):</span>
                                            <span className="font-medium">65% of annual budget</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Average Monthly:</span>
                                            <span className="font-medium">₱175K</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-gray-50 p-4 rounded-lg">
                                    <h4 className="font-medium text-gray-900 mb-3">Service Category Trends</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span>Medical Assistance:</span>
                                            <span className="font-medium text-blue-600">45% (+8% annually)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Financial Aid:</span>
                                            <span className="font-medium text-green-600">30% (+5% annually)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Educational Support:</span>
                                            <span className="font-medium text-purple-600">15% (+12% annually)</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span>Burial Assistance:</span>
                                            <span className="font-medium text-orange-600">10% (stable)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Risk Analysis */}
                            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                <h4 className="font-medium text-yellow-900 mb-2">Budget Risk Analysis</h4>
                                <p className="text-sm text-yellow-800 mb-3">
                                    Historical data shows budget depletion typically occurs in <strong>October-November</strong>,
                                    requiring supplementary budget requests. Recommended buffer: <strong>15-20%</strong> above trend projection.
                                </p>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-yellow-700">2022 Shortfall:</span>
                                        <span className="font-medium ml-2">₱300K (Oct)</span>
                                    </div>
                                    <div>
                                        <span className="text-yellow-700">2023 Shortfall:</span>
                                        <span className="font-medium ml-2">₱450K (Nov)</span>
                                    </div>
                                </div>
                            </div>

                            <div className="flex gap-3">
                                <Button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white">
                                    View Detailed Analysis
                                </Button>
                                <Button variant="outline" className="flex-1 border-purple-200 text-purple-700">
                                    Export Historical Report
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Recommendations */}
                <Card className="border-green-200">
                    <CardHeader>
                        <CardTitle className="text-green-900 flex items-center">
                            <Target className="h-5 w-5 mr-2" />
                            AI-Powered Recommendations
                        </CardTitle>
                        <CardDescription className="text-green-600">
                            Smart recommendations based on data analysis
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                <h4 className="font-medium text-green-900 mb-2">Budget Reallocation Suggestion</h4>
                                <p className="text-sm text-green-700">Consider increasing medical assistance budget by 12% based on recent demand trends.</p>
                            </div>
                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <h4 className="font-medium text-blue-900 mb-2">Resource Optimization</h4>
                                <p className="text-sm text-blue-700">Barangay Poblacion shows 23% higher efficiency in program delivery. Consider expanding successful practices.</p>
                            </div>
                            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                <h4 className="font-medium text-orange-900 mb-2">Risk Alert</h4>
                                <p className="text-sm text-orange-700">Educational assistance budget may exceed allocation by Q3. Consider early intervention strategies.</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
