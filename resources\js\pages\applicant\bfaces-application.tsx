import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import AppLayout from "@/layouts/app-layout";
import { type BreadcrumbItem } from "@/types";
import { Head, useForm } from "@inertiajs/react";
    import React from "react";
import { useState } from "react";
    import { CircleDot, CheckCircle, ArrowRight, ArrowLeft, HelpCircle, PlusCircle, Trash2 } from "lucide-react";
    import { Label } from "@/components/ui/label";
    import { Input } from "@/components/ui/input";
    import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
    import { Textarea } from "@/components/ui/textarea";
    import { Checkbox } from "@/components/ui/checkbox";
    import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
    import { type CheckedState } from "@radix-ui/react-checkbox";

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: "Dashboard",
        href: "/dashboard",
    },
    {
        title: "BFACES Application",
        href: "/bfaces-application",
    },
];

// Dummy data for application status
const applicationStatus = "unfilled"; // unfilled, submitted, under_review, verified, rejected

    // Updated steps based on physical form
const steps = [
        { id: "location_family_head", title: "Location & Family Head" },
        { id: "personal_details", title: "Personal Details" },
        { id: "household_conditions", title: "Household Conditions" },
        { id: "family_composition", title: "Family Composition" },
        { id: "housing_status", title: "Housing Status" },
    { id: "documents", title: "Supporting Documents" },
    { id: "review", title: "Review & Submit" },
];

    type FormDataConvertible = string | number | boolean | File | Blob | Date | null | undefined;

    // Interface for Family Member
    interface FamilyMember {
        name: string;
        relation: string;
        birthday: string;
        gender: string;
        age: string;
        education: string;
        skills: string;
        income: string;
    }

    // Interface for form data
    interface BFACESFormData {
        [key: string]: FormDataConvertible;
        region: string;
        provinceDistrict: string;
        municipality: string;
        barangayEvacCenter: string;
        serialNo: string;
        surname: string;
        firstName: string;
        middleName: string;
        sex: string;
        age: string;
        completeAddress: string;
        contactNumber: string;
        dateOfBirth: string;
        placeOfBirth: string;
        civilStatus: string;
        occupation: string;
        educationalAttainment: string;
        religion: string;
        lightSource: string;
        waterSource: string;
        houseMaterial: string;
        monthlyNetIncome: string;
        is4psBeneficiary: boolean;
        isIp: boolean;
        ethnicity: string;
        sourceOfInformation: string;
        familyMembers: string; // Will store JSON string of FamilyMember[]
        housingStatus: string;
        validId: File | null;
        confirmAccuracy: boolean;
    }

export default function BFACESApplication() {
    const [currentStep, setCurrentStep] = useState(0);
    const [submittedStatus, setSubmittedStatus] = useState(applicationStatus);

        const { data, setData, post, processing, errors, reset } = useForm<BFACESFormData>({
            // Location
            region: "",
            provinceDistrict: "Bulacan", // Default assumption
            municipality: "Balagtas", // Default assumption
            barangayEvacCenter: "",
            serialNo: "", // May be assigned upon submission

            // Head of Family
            surname: "",
            firstName: "",
            middleName: "",
            sex: "", // 'M' or 'F'
            age: "",

            // Personal Details
            completeAddress: "",
            contactNumber: "",
            dateOfBirth: "",
            placeOfBirth: "",
            civilStatus: "",
            occupation: "",
            educationalAttainment: "",
            religion: "",

            // Household Conditions
            lightSource: "", // e.g., Electric, Kerosene
            waterSource: "", // e.g., Own Faucet, Public Well
            houseMaterial: "", // e.g., Concrete, Light Materials
            monthlyNetIncome: "",
            is4psBeneficiary: false,
            isIp: false, // Indigenous Person
            ethnicity: "",
            sourceOfInformation: "",

            // Family Composition
            familyMembers: JSON.stringify([]),

            // Housing Status
            housingStatus: "", // Map from checkboxes later

            // Crisis Details (Keep or remove based on final flow)
            // crisisType: "",
            // crisisDescription: "",
            // crisisDate: "",

            // Documents (General ID for BFACES)
        validId: null as File | null,
            // proofOfCrisis: null as File | null, // Might belong to service application, not BFACES registration
            
            // Confirmation
            confirmAccuracy: false, // Added for final step confirmation
        });

        // Function to add a family member
        const addFamilyMember = () => {
            const currentMembers: FamilyMember[] = JSON.parse(data.familyMembers);
            setData('familyMembers', JSON.stringify([
                ...currentMembers,
                { name: '', relation: '', birthday: '', gender: '', age: '', education: '', skills: '', income: '' }
            ]));
        };

        // Function to remove a family member
        const removeFamilyMember = (index: number) => {
            const currentMembers: FamilyMember[] = JSON.parse(data.familyMembers);
            setData('familyMembers', JSON.stringify(currentMembers.filter((_, i) => i !== index)));
        };

        // Function to update a family member's details
        const updateFamilyMember = (index: number, field: keyof FamilyMember, value: string) => {
            const currentMembers: FamilyMember[] = JSON.parse(data.familyMembers);
            const updatedMembers = [...currentMembers];
            updatedMembers[index] = { ...updatedMembers[index], [field]: value };
            setData('familyMembers', JSON.stringify(updatedMembers));
        };

    const isFirstStep = currentStep === 0;
    const isLastStep = currentStep === steps.length - 1;

    const next = () => {
        if (!isLastStep) {
            setCurrentStep((step) => step + 1);
        }
    };

    const prev = () => {
        if (!isFirstStep) {
            setCurrentStep((step) => step - 1);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!isLastStep) {
            next();
            return;
        }
        
            // Actual submission logic
            // post(route('bfaces.store'), { // Example route
            //     onSuccess: () => {
            //         setSubmittedStatus("submitted"); 
            //         reset(); // Clear form on success
            //     },
            //     onError: () => {
            //         // Handle errors, maybe stay on the review step
            //         console.error("Submission failed", errors);
            //     }
            // });

            // Temporary submission for demo
            console.log("Form Data Submitted:", data);
        setSubmittedStatus("submitted");
            alert("BFACES Application submitted successfully! (This is a demo - check console for data)");
    };

    // Different content based on application status
    if (submittedStatus === "submitted" || submittedStatus === "under_review" || submittedStatus === "verified" || submittedStatus === "rejected") {
        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                    <Head title="BFACES Application Status" />
                <div className="flex flex-col gap-6 p-6">
                    <div className="flex items-center justify-between">
                            <h1 className="text-2xl font-bold tracking-tight">BFACES Application Status</h1>
                            {/* Button to start new application if needed */}
                            {(submittedStatus === "rejected" || submittedStatus === "verified") && (
                                <Button onClick={() => { setSubmittedStatus("unfilled"); reset(); setCurrentStep(0); }} variant="outline" style={{ cursor: 'pointer' }}>
                                    {submittedStatus === "rejected" ? "Reapply" : "Start New Application"}
                                </Button>
                            )}
                    </div>

                    <Card>
                        <CardHeader>
                            <div className="flex items-center gap-2">
                                {submittedStatus === "verified" ? (
                                    <CheckCircle className="h-6 w-6 text-green-500" />
                                ) : submittedStatus === "rejected" ? (
                                    <HelpCircle className="h-6 w-6 text-red-500" />
                                ) : (
                                    <CircleDot className="h-6 w-6 text-amber-500" />
                                )}
                                <CardTitle>
                                    Application Status:{" "}
                                    {submittedStatus === "submitted"
                                        ? "Submitted"
                                        : submittedStatus === "under_review"
                                        ? "Under Review"
                                        : submittedStatus === "verified"
                                            ? "Verified"
                                        : "Rejected"}
                                </CardTitle>
                            </div>
                            <CardDescription>
                                {submittedStatus === "submitted"
                                    ? "Your family registry has been submitted and is awaiting initial review."
                                    : submittedStatus === "under_review"
                                    ? "Your family registry is currently being reviewed by our staff."
                                    : submittedStatus === "verified"
                                        ? "Your family registry has been verified. You can now apply for social services for any family member."
                                    : "Your family registry has been rejected. Please review the feedback and reapply if necessary."}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium">Application Reference</h3>
                                        <p className="text-muted-foreground">{data.serialNo || "BFACES-XXXX-XXXX"}</p> 
                                </div>
                                <div>
                                    <h3 className="font-medium">Submission Date</h3>
                                        <p className="text-muted-foreground">{new Date().toLocaleDateString()}</p>
                                </div>
                                {submittedStatus === "rejected" && (
                                    <div>
                                        <h3 className="font-medium text-red-500">Rejection Reason</h3>
                                        <p className="text-muted-foreground">
                                                Insufficient documentation provided or information mismatch.
                                        </p>
                                    </div>
                                )}
                                {submittedStatus === "verified" && (
                                    <div>
                                        <h3 className="font-medium text-green-500">Next Steps</h3>
                                        <p className="text-muted-foreground">
                                            You can now apply for available social services for any family member through the Services section.
                                        </p>
                                            <Button className="mt-4" asChild style={{ cursor: 'pointer' }}>
                                                <a href="/services">Go to Services</a>
                                            </Button>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="BFACES Application" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                        <h1 className="text-2xl font-bold tracking-tight">BFACES Family Registry Form</h1>
                </div>

                {/* Step indicator */}
                    <div className="mb-8 flex justify-between overflow-x-auto pb-2">
                    {steps.map((step, index) => (
                            <div key={step.id} className="relative flex flex-col items-center px-2 min-w-[100px]">
                                {index < steps.length - 1 && (
                                    <div className={`absolute left-1/2 top-4 h-0.5 w-full ${index < currentStep ? 'bg-primary' : 'bg-muted'}`} style={{ transform: 'translateX(50%)' }}></div>
                                )}
                            <div
                                    className={`relative z-10 flex h-7 w-7 items-center justify-center rounded-full border-2 ${
                                    index < currentStep
                                        ? "border-primary bg-primary text-primary-foreground"
                                        : index === currentStep
                                            ? "border-primary bg-background text-primary ring-2 ring-primary ring-offset-2"
                                            : "border-muted bg-background text-muted-foreground"
                                }`}
                            >
                                {index < currentStep ? (
                                    <CheckCircle className="h-4 w-4" />
                                ) : (
                                        <span className="text-xs font-medium">{index + 1}</span>
                                )}
                            </div>
                            <span
                                    className={`mt-2 text-center text-xs ${
                                        index <= currentStep ? "font-medium text-primary" : "text-muted-foreground"
                                }`}
                            >
                                {step.title}
                            </span>
                        </div>
                    ))}
                </div>

                {/* Form */}
                <Card>
                    <form onSubmit={handleSubmit}>
                        <CardHeader>
                            <CardTitle>{steps[currentStep].title}</CardTitle>
                            <CardDescription>
                                    {currentStep === 0 && "Provide location details and information about the head of the family."}
                                    {currentStep === 1 && "Enter the personal details of the head of the family."}
                                    {currentStep === 2 && "Describe the household's living conditions and income sources."}
                                    {currentStep === 3 && "List all members residing in the household."}
                                    {currentStep === 4 && "Indicate the family's current housing situation."}
                                    {currentStep === 5 && "Upload required identification document(s)."}
                                    {currentStep === 6 && "Review all entered information before submitting."}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                                {/* Step 1: Location & Family Head */}
                            {currentStep === 0 && (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="md:col-span-2 font-semibold text-lg mb-2">Location</div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="region">Region</Label>
                                            <Input id="region" value={String(data.region || 3)} onChange={e => setData('region', e.target.value)} />
                                            {errors.region && <p className="text-sm text-red-500">{errors.region}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="provinceDistrict">Province/District</Label>
                                            <Input id="provinceDistrict" value={String(data.provinceDistrict)} onChange={e => setData('provinceDistrict', e.target.value)} />
                                            {errors.provinceDistrict && <p className="text-sm text-red-500">{errors.provinceDistrict}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="municipality">Municipality</Label>
                                            <Input id="municipality" value={String(data.municipality)} onChange={e => setData('municipality', e.target.value)} />
                                            {errors.municipality && <p className="text-sm text-red-500">{errors.municipality}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="barangayEvacCenter">Barangay / Evacuation Center</Label>
                                            <Select 
                                                defaultValue={data.barangayEvacCenter} 
                                                onValueChange={(value) => setData('barangayEvacCenter', value)}
                                            required
                                            >
                                                <SelectTrigger className="bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg">
                                                    <SelectValue placeholder="Select your barangay or evacuation center" />
                                                </SelectTrigger>
                                                <SelectContent className="border-purple-300 hover:border-purple-400">
                                                    {['Borol 1st', 'Borol 2nd', 'Dalig', 'Longos', 'Panginay', 'Pulong Gubat', 'San Juan', 'Santol', 'Wawa'].map(barangay => (
                                                        <SelectItem key={barangay} value={barangay}>{barangay}</SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.barangayEvacCenter && <p className="text-sm text-red-500">{errors.barangayEvacCenter}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="serialNo">Serial No.</Label>
                                            <Input id="serialNo" value={String(data.serialNo)} onChange={e => setData('serialNo', e.target.value)} placeholder="Assigned upon submission" disabled />
                                            {errors.serialNo && <p className="text-sm text-red-500">{errors.serialNo}</p>}
                                    </div>

                                        <div className="md:col-span-2 my-4 border-t"></div>

                                        <div className="md:col-span-2 font-semibold text-lg mb-2">Head of the Family</div>

                                        <div className="grid gap-1.5">
                                            <Label htmlFor="surname">Surname</Label>
                                            <Input id="surname" value={String(data.surname)} onChange={e => setData('surname', e.target.value)} required />
                                            {errors.surname && <p className="text-sm text-red-500">{errors.surname}</p>}
                                            </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="firstName">First Name</Label>
                                            <Input id="firstName" value={String(data.firstName)} onChange={e => setData('firstName', e.target.value)} required />
                                            {errors.firstName && <p className="text-sm text-red-500">{errors.firstName}</p>}
                                    </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="middleName">Middle Name</Label>
                                            <Input id="middleName" value={String(data.middleName)} onChange={e => setData('middleName', e.target.value)} />
                                            {errors.middleName && <p className="text-sm text-red-500">{errors.middleName}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label>Sex</Label>
                                            <RadioGroup
                                                value={String(data.sex)}
                                                onValueChange={(value: string) => setData('sex', value)}
                                                className="flex gap-4 pt-2"
                                                required
                                            >
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="M" id="sex-m" />
                                                    <Label htmlFor="sex-m">Male</Label>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <RadioGroupItem value="F" id="sex-f" />
                                                    <Label htmlFor="sex-f">Female</Label>
                                                </div>
                                            </RadioGroup>
                                        {errors.sex && <p className="text-sm text-red-500">{errors.sex}</p>}
                                    </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="age">Age</Label>
                                            <Input id="age" type="number" value={String(data.age)} onChange={e => setData('age', e.target.value)} required />
                                            {errors.age && <p className="text-sm text-red-500">{errors.age}</p>}
                                        </div>
                                    </div>
                                )}

                                {/* Step 2: Personal Details */}
                                {currentStep === 1 && (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="md:col-span-2 grid gap-1.5">
                                            <Label htmlFor="completeAddress">Complete Address</Label>
                                            <Input id="completeAddress" value={String(data.completeAddress)} onChange={e => setData('completeAddress', e.target.value)} required />
                                            {errors.completeAddress && <p className="text-sm text-red-500">{errors.completeAddress}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="contactNumber">Contact Number</Label>
                                            <Input id="contactNumber" type="tel" value={String(data.contactNumber)} onChange={e => setData('contactNumber', e.target.value)} required />
                                        {errors.contactNumber && <p className="text-sm text-red-500">{errors.contactNumber}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="dateOfBirth">Date of Birth</Label>
                                            <Input id="dateOfBirth" type="date" value={String(data.dateOfBirth)} onChange={e => setData('dateOfBirth', e.target.value)} required />
                                            {errors.dateOfBirth && <p className="text-sm text-red-500">{errors.dateOfBirth}</p>}
                                            </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="placeOfBirth">Place of Birth</Label>
                                            <Input id="placeOfBirth" value={String(data.placeOfBirth)} onChange={e => setData('placeOfBirth', e.target.value)} />
                                            {errors.placeOfBirth && <p className="text-sm text-red-500">{errors.placeOfBirth}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="civilStatus">Civil Status</Label>
                                            <Select value={String(data.civilStatus)} onValueChange={(value: string) => setData('civilStatus', value)}>
                                                <SelectTrigger id="civilStatus">
                                                    <SelectValue placeholder="Select status..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="Single">Single</SelectItem>
                                                    <SelectItem value="Married">Married</SelectItem>
                                                    <SelectItem value="Widowed">Widowed</SelectItem>
                                                    <SelectItem value="Separated">Separated</SelectItem>
                                                    <SelectItem value="Annulled">Annulled</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.civilStatus && <p className="text-sm text-red-500">{errors.civilStatus}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="occupation">Occupation</Label>
                                            <Input id="occupation" value={String(data.occupation)} onChange={e => setData('occupation', e.target.value)} />
                                                {errors.occupation && <p className="text-sm text-red-500">{errors.occupation}</p>}
                                            </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="educationalAttainment">Educational Attainment</Label>
                                            <Input id="educationalAttainment" value={String(data.educationalAttainment)} onChange={e => setData('educationalAttainment', e.target.value)} />
                                            {errors.educationalAttainment && <p className="text-sm text-red-500">{errors.educationalAttainment}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="religion">Religion</Label>
                                            <Input id="religion" value={String(data.religion)} onChange={e => setData('religion', e.target.value)} />
                                            {errors.religion && <p className="text-sm text-red-500">{errors.religion}</p>}
                                        </div>
                                </div>
                            )}

                                {/* Step 3: Household Conditions */}
                                {currentStep === 2 && (
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="lightSource">Light Source</Label>
                                            <Input id="lightSource" value={String(data.lightSource)} onChange={e => setData('lightSource', e.target.value)} placeholder="e.g., Electric, Solar, Kerosene Lamp"/>
                                            {errors.lightSource && <p className="text-sm text-red-500">{errors.lightSource}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="waterSource">Water Source</Label>
                                            <Input id="waterSource" value={String(data.waterSource)} onChange={e => setData('waterSource', e.target.value)} placeholder="e.g., Own Faucet, Public Well, Spring" />
                                            {errors.waterSource && <p className="text-sm text-red-500">{errors.waterSource}</p>}
                                        </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="houseMaterial">House Material</Label>
                                            <Input id="houseMaterial" value={String(data.houseMaterial)} onChange={e => setData('houseMaterial', e.target.value)} placeholder="e.g., Concrete, Wood, Light Materials" />
                                            {errors.houseMaterial && <p className="text-sm text-red-500">{errors.houseMaterial}</p>}
                                    </div>
                                        <div className="grid gap-1.5">
                                            <Label htmlFor="monthlyNetIncome">Monthly Net Income (PHP)</Label>
                                            <Input id="monthlyNetIncome" type="number" value={String(data.monthlyNetIncome)} onChange={e => setData('monthlyNetIncome', e.target.value)} required />
                                            {errors.monthlyNetIncome && <p className="text-sm text-red-500">{errors.monthlyNetIncome}</p>}
                                    </div>

                                        <div className="md:col-span-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
                                            <div className="flex items-center space-x-2 pt-2">
                                                <Checkbox 
                                                    id="is4psBeneficiary" 
                                                    checked={Boolean(data.is4psBeneficiary)} 
                                                    onCheckedChange={(checked: CheckedState) => setData('is4psBeneficiary', Boolean(checked))} 
                                                />
                                                <Label htmlFor="is4psBeneficiary">4Ps Beneficiary</Label>
                                                {errors.is4psBeneficiary && <p className="text-sm text-red-500">{errors.is4psBeneficiary}</p>}
                                            </div>
                                            <div className="flex items-center space-x-2 pt-2">
                                                <Checkbox 
                                                    id="isIp" 
                                                    checked={Boolean(data.isIp)} 
                                                    onCheckedChange={(checked: CheckedState) => setData('isIp', Boolean(checked))} 
                                                />
                                                <Label htmlFor="isIp">IP (Indigenous Person)</Label>
                                                {errors.isIp && <p className="text-sm text-red-500">{errors.isIp}</p>}
                                            </div>
                                        </div>

                                        {data.isIp && (
                                            <div className="md:col-span-2 grid gap-1.5">
                                                <Label htmlFor="ethnicity">If IP, Type of Ethnicity</Label>
                                                <Input id="ethnicity" value={String(data.ethnicity)} onChange={e => setData('ethnicity', e.target.value)} />
                                                {errors.ethnicity && <p className="text-sm text-red-500">{errors.ethnicity}</p>}
                                            </div>
                                        )}

                                        <div className="md:col-span-2 grid gap-1.5">
                                            <Label htmlFor="sourceOfInformation">Source of Information</Label>
                                            <Input id="sourceOfInformation" value={String(data.sourceOfInformation)} onChange={e => setData('sourceOfInformation', e.target.value)} placeholder="How did you hear about this?" />
                                            {errors.sourceOfInformation && <p className="text-sm text-red-500">{errors.sourceOfInformation}</p>}
                                        </div>
                                    </div>
                                )}

                                {/* Step 4: Family Composition */}
                                {currentStep === 3 && (
                                    <div className="space-y-4">
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full divide-y divide-gray-200 border">
                                                <thead className="bg-gray-50">
                                                    <tr>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Relation</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birthday</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Educ.</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skills</th>
                                                        <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Income</th>
                                                        <th scope="col" className="relative px-3 py-2">
                                                            <span className="sr-only">Remove</span>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="bg-white divide-y divide-gray-200">
                                                    {((): React.ReactElement => {
                                                        const familyMembers: FamilyMember[] = JSON.parse(data.familyMembers);
                                                        return familyMembers.length === 0 ? (
                                                            <tr>
                                                                <td colSpan={9} className="px-3 py-4 text-center text-sm text-gray-500">No family members added yet.</td>
                                                            </tr>
                                                        ) : (
                                                            <>
                                                                {familyMembers.map((member: FamilyMember, index: number) => (
                                                                    <tr key={index}>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input className="min-w-[150px]" value={String(member.name)} onChange={(e) => updateFamilyMember(index, 'name', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input className="min-w-[100px]" value={String(member.relation)} onChange={(e) => updateFamilyMember(index, 'relation', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input type="date" className="min-w-[140px]" value={String(member.birthday)} onChange={(e) => updateFamilyMember(index, 'birthday', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap">
                                                                            <Select value={String(member.gender)} onValueChange={(value: string) => updateFamilyMember(index, 'gender', value)}>
                                                                                <SelectTrigger className="min-w-[80px]">
                                                                                    <SelectValue placeholder="-" />
                                                                                </SelectTrigger>
                                                                                <SelectContent>
                                                                                    <SelectItem value="M">M</SelectItem>
                                                                                    <SelectItem value="F">F</SelectItem>
                                                                                </SelectContent>
                                                                            </Select>
                                                                        </td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input type="number" className="min-w-[70px]" value={String(member.age)} onChange={(e) => updateFamilyMember(index, 'age', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input className="min-w-[100px]" value={String(member.education)} onChange={(e) => updateFamilyMember(index, 'education', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input className="min-w-[120px]" value={String(member.skills)} onChange={(e) => updateFamilyMember(index, 'skills', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap"><Input type="number" className="min-w-[100px]" value={String(member.income)} onChange={(e) => updateFamilyMember(index, 'income', e.target.value)} /></td>
                                                                        <td className="px-3 py-2 whitespace-nowrap text-right text-sm font-medium">
                                                                            <Button type="button" variant="ghost" size="icon" onClick={() => removeFamilyMember(index)} style={{ cursor: 'pointer' }}>
                                                                                <Trash2 className="h-4 w-4 text-red-500" />
                                                                            </Button>
                                                                        </td>
                                                                    </tr>
                                                                ))}
                                                            </>
                                                        );
                                                    })()}
                                                </tbody>
                                            </table>
                                        </div>
                                        <Button type="button" variant="outline" onClick={addFamilyMember} className="mt-4" style={{ cursor: 'pointer' }}>
                                            <PlusCircle className="mr-2 h-4 w-4" /> Add Family Member
                                        </Button>
                                </div>
                            )}

                                {/* Step 5: Housing Status */}
                                {currentStep === 4 && (
                                    <div className="grid gap-2">
                                        <Label className="font-medium">Housing Status</Label>
                                        <RadioGroup
                                            value={String(data.housingStatus)}
                                            onValueChange={(value: string) => setData('housingStatus', value)}
                                            className="space-y-2"
                                        >
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="house_lot_owner" id="hs-1" />
                                                <Label htmlFor="hs-1">House & lot owner</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="rented_house_lot" id="hs-2" />
                                                <Label htmlFor="hs-2">Rented house & lot</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="house_owner_lot_renter" id="hs-3" />
                                                <Label htmlFor="hs-3">House owner & lot renter</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="house_owner_rent_free_lot_owners_consent" id="hs-4" />
                                                <Label htmlFor="hs-4">House owner, rent-free lot with owner's consent</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="house_owner_rent_free_wo_consent" id="hs-5" />
                                                <Label htmlFor="hs-5">House owner, rent-free w/o consent of owner</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="rent_free_house_lot_owners_consent" id="hs-6" />
                                                <Label htmlFor="hs-6">Rent-free house & lot with owner's consent</Label>
                                    </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="rent_free_house_lot_wo_consent" id="hs-7" />
                                                <Label htmlFor="hs-7">Rent-free house & lot w/o owner's consent</Label>
                                    </div>
                                        </RadioGroup>
                                        {errors.housingStatus && <p className="text-sm text-red-500">{errors.housingStatus}</p>}
                                </div>
                            )}

                                {/* Step 6: Supporting Documents */}
                                {currentStep === 5 && (
                                <div className="space-y-4">
                                    <div className="rounded-lg border p-4">
                                            <h3 className="font-medium">Required Document</h3>
                                        <ul className="mt-2 list-inside list-disc text-sm text-muted-foreground">
                                                <li>Valid Government-Issued ID (applicant/head of family)</li>
                                        </ul>
                                    </div>

                                        <div className="grid gap-1.5">
                                            <Label htmlFor="validId">Upload Valid ID</Label>
                                            <Input
                                            id="validId"
                                            type="file"
                                            accept=".pdf,.jpg,.jpeg,.png"
                                            onChange={(e) => 
                                                e.target.files && setData("validId", e.target.files[0])
                                            }
                                                className="w-full"
                                            required
                                        />
                                            {data.validId instanceof File && <p className="text-xs text-muted-foreground mt-1">Selected: {data.validId.name}</p>}
                                        {errors.validId && <p className="text-sm text-red-500">{errors.validId}</p>}
                                    </div>

                                    <div className="rounded-lg bg-muted p-4 text-sm">
                                        <p>
                                                <strong>Note:</strong> Document must be clear and legible. Maximum file size is 5MB.
                                                Accepted formats: PDF, JPG, PNG. This ID verifies the identity of the Head of Family applying for BFACES.
                                        </p>
                                    </div>
                                </div>
                            )}


                                {/* Step 7: Review & Submit */}
                                {currentStep === 6 && (
                                <div className="space-y-6">
                                        <h3 className="text-lg font-semibold mb-4">Review Your Application Details</h3>

                                        <ReviewSection title="Location & Family Head">
                                            <ReviewItem label="Region" value={String(data.region)} />
                                            <ReviewItem label="Province/District" value={String(data.provinceDistrict)} />
                                            <ReviewItem label="Municipality" value={String(data.municipality)} />
                                            <ReviewItem label="Barangay/Evac Center" value={String(data.barangayEvacCenter)} />
                                            <ReviewItem label="Surname" value={String(data.surname)} />
                                            <ReviewItem label="First Name" value={String(data.firstName)} />
                                            <ReviewItem label="Middle Name" value={String(data.middleName)} />
                                            <ReviewItem label="Sex" value={String(data.sex)} />
                                            <ReviewItem label="Age" value={String(data.age)} />
                                        </ReviewSection>

                                        <ReviewSection title="Personal Details">
                                            <ReviewItem label="Complete Address" value={String(data.completeAddress)} fullWidth/>
                                            <ReviewItem label="Contact Number" value={String(data.contactNumber)} />
                                            <ReviewItem label="Date of Birth" value={String(data.dateOfBirth)} />
                                            <ReviewItem label="Place of Birth" value={String(data.placeOfBirth)} />
                                            <ReviewItem label="Civil Status" value={String(data.civilStatus)} />
                                            <ReviewItem label="Occupation" value={String(data.occupation)} />
                                            <ReviewItem label="Educational Attainment" value={String(data.educationalAttainment)} />
                                            <ReviewItem label="Religion" value={String(data.religion)} />
                                        </ReviewSection>

                                        <ReviewSection title="Household Conditions">
                                            <ReviewItem label="Light Source" value={String(data.lightSource)} />
                                            <ReviewItem label="Water Source" value={String(data.waterSource)} />
                                            <ReviewItem label="House Material" value={String(data.houseMaterial)} />
                                            <ReviewItem label="Monthly Net Income" value={`₱ ${String(data.monthlyNetIncome)}`} />
                                            <ReviewItem label="4Ps Beneficiary" value={data.is4psBeneficiary ? 'Yes' : 'No'} />
                                            <ReviewItem label="Indigenous Person" value={data.isIp ? 'Yes' : 'No'} />
                                            {data.isIp && <ReviewItem label="Ethnicity" value={String(data.ethnicity)} />}
                                            <ReviewItem label="Source of Information" value={String(data.sourceOfInformation)} />
                                        </ReviewSection>

                                        <ReviewSection title="Family Composition">
                                            {((): React.ReactElement => {
                                                const familyMembers: FamilyMember[] = JSON.parse(data.familyMembers);
                                                return familyMembers.length > 0 ? (
                                                    <div className="overflow-x-auto">
                                                        <table className="min-w-full divide-y divide-gray-200 border text-sm">
                                                            <thead><tr><th>Name</th><th>Relation</th><th>Age</th><th>Income</th></tr></thead>
                                                            <tbody>
                                                                {familyMembers.map((m: FamilyMember, i: number) => (
                                                                    <tr key={i}><td>{String(m.name)}</td><td>{String(m.relation)}</td><td>{String(m.age)}</td><td>{String(m.income)}</td></tr>
                                                                ))}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                ) : (
                                                    <p className="text-sm text-muted-foreground">No family members listed.</p>
                                                );
                                            })()}
                                        </ReviewSection>

                                        <ReviewSection title="Housing Status">
                                            <ReviewItem label="Status" value={String(data.housingStatus).replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not specified'} fullWidth/>
                                        </ReviewSection>

                                        <ReviewSection title="Uploaded Documents">
                                            <ReviewItem label="Valid ID" value={data.validId ? data.validId.name : "Not uploaded"} />
                                        </ReviewSection>

                                        <div className="rounded-lg border bg-muted/50 p-4">
                                            <div className="flex items-start space-x-3">
                                                <Checkbox
                                                    id="confirmAccuracy"
                                                    checked={Boolean(data.confirmAccuracy)}
                                                    onCheckedChange={(checked: CheckedState) => setData('confirmAccuracy', Boolean(checked))}
                                                    required
                                                />
                                                <div className="grid gap-1.5 leading-none">
                                                    <Label htmlFor="confirmAccuracy" className="font-medium">
                                                        Confirmation of Accuracy
                                                    </Label>
                                                    <p className="text-sm text-muted-foreground">
                                                    I confirm that all the information provided is true and accurate. I understand that
                                                    providing false information may result in the rejection of my application and possible
                                                    legal consequences.
                                                    </p>
                                                </div>
                                            </div>
                                            {errors.confirmAccuracy && <p className="mt-2 text-sm text-red-500">{errors.confirmAccuracy}</p>}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                            <CardFooter className="flex justify-between border-t pt-4 mt-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={prev}
                                    disabled={isFirstStep || processing}
                                className={isFirstStep ? "invisible" : ""}
                                    style={{ cursor: 'pointer' }}
                            >
                                <ArrowLeft className="mr-2 h-4 w-4" /> Previous
                            </Button>
                                <Button type="submit" disabled={processing || (isLastStep && !data.confirmAccuracy)} style={{ cursor: 'pointer' }}>
                                    {processing ? 'Submitting...' : isLastStep ? "Submit Application" : "Next"}
                                    {!isLastStep && !processing && <ArrowRight className="ml-2 h-4 w-4" />}
                            </Button>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </AppLayout>
    );
} 

    interface ReviewSectionProps {
        title: string;
        children: React.ReactNode;
    }

    const ReviewSection: React.FC<ReviewSectionProps> = ({ title, children }) => (
        <div>
            <h4 className="mb-3 text-md font-semibold border-b pb-1">{title}</h4>
            <div className="grid grid-cols-1 gap-x-4 gap-y-2 text-sm sm:grid-cols-2">
                {children}
            </div>
        </div>
    );

    interface ReviewItemProps {
        label: string;
        value: string | number | undefined | null;
        fullWidth?: boolean;
    }

    const ReviewItem: React.FC<ReviewItemProps> = ({ label, value, fullWidth = false }) => (
        <div className={fullWidth ? 'sm:col-span-2' : ''}>
            <p className="font-medium">{label}</p>
            <p className="text-muted-foreground">{value !== null && value !== undefined && String(value) !== '' ? String(value) : <span className="italic">Not provided</span>}</p>
        </div>
    ); 