import { <PERSON>, <PERSON> } from "@inertiajs/react"
import AppLayout from "@/layouts/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, Filter, Plus, Search, Settings2, Layers, FileText, GitBranch } from "lucide-react"
import { useState } from "react"
import { stats } from "@/pages/mswdo-officer/tempData/services"

// Mock data since we don't have these in temp data yet
const categories = [
  { id: 1, name: 'Medical Assistance' },
  { id: 2, name: 'Educational Support' },
  { id: 3, name: 'Housing Assistance' },
  { id: 4, name: 'Financial Aid' }
];

const services = [
  {
    id: 1,
    category: { id: 1, name: 'Medical Assistance' },
    name: 'Hospital Bill Assistance',
    description: 'Financial assistance for hospital bills',
    slug: 'hospital-bill-assistance',
    max_amount: 10000,
    processing_days: 5,
    daily_quota: 10,
    weekly_quota: 50,
    monthly_quota: 200,
    eligibility_criteria: ['Resident of Balagtas', 'Below poverty line'],
    schedule: {
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      hours: '8:00 AM - 5:00 PM'
    },
    requires_verification: true,
    is_active: true,
    requirements_count: 5,
    workflow_stages_count: 3,
    created_at: '2024-01-01',
    updated_at: '2024-04-09'
  }
];

interface Service {
  id: number
  category: {
    id: number
    name: string
  }
  name: string
  description: string
  slug: string
  max_amount: number | null
  processing_days: number
  daily_quota: number | null
  weekly_quota: number | null
  monthly_quota: number | null
  eligibility_criteria: string[]
  schedule: {
    days: string[]
    hours: string
  }
  requires_verification: boolean
  is_active: boolean
  requirements_count: number
  workflow_stages_count: number
  created_at: string
  updated_at: string
}

interface Category {
  id: number
  name: string
  description: string | null
  slug: string
  icon: string | null
  is_active: boolean
  services_count: number
}

interface Props {
  categories: Category[]
  services: Service[]
  totalServices: number
  activeServices: number
  totalCategories: number
  activeCategories: number
}

export default function ServicesConfiguration() {
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState<number | "all">("all")
  const [statusFilter, setStatusFilter] = useState<"active" | "inactive" | "all">("all")

  const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Case and Service Management',
        href: '#',
    },
    {
        title: 'Services Programs',
        href: '/mswdo-officer/cases',
    },
];

  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = categoryFilter === "all" || service.category.id === categoryFilter
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "active" && service.is_active) ||
      (statusFilter === "inactive" && !service.is_active)

    return matchesSearch && matchesCategory && matchesStatus
  })

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Service Programs" />

      <div className="flex flex-col gap-6 p-4 md:p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Service Programs</h1>
            <p className="text-muted-foreground mt-1 text-sm md:text-base">Manage social welfare service programs and configurations</p>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <Heading 
            title="Services Configuration" 
            description="Configure and manage social services and their categories." 
          />
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <a href="/superadmin/services/categories/new">
                <Layers className="mr-2 h-4 w-4" />
                New Category
              </a>
            </Button>
            <Button asChild>
              <a href="/superadmin/services/new">
                <Plus className="mr-2 h-4 w-4" />
                New Service
              </a>
            </Button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Services
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalServices}</div>
              <p className="text-xs text-muted-foreground">
                Configured services
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Services
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.serviceMetrics.active}</div>
              <p className="text-xs text-muted-foreground">
                Currently enabled
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.length}</div>
              <p className="text-xs text-muted-foreground">
                Service categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{categories.filter(c => true).length}</div>
              <p className="text-xs text-muted-foreground">
                Enabled categories
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search services..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-2">
                <div className="flex items-center gap-1.5">
                  <Label htmlFor="category" className="text-sm">
                    Category
                  </Label>
                  <select
                    id="category"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={categoryFilter}
                    onChange={e => setCategoryFilter(e.target.value === "all" ? "all" : Number(e.target.value))}
                  >
                    <option value="all">All</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center gap-1.5">
                  <Label htmlFor="status" className="text-sm">
                    Status
                  </Label>
                  <select
                    id="status"
                    className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value as "active" | "inactive" | "all")}
                  >
                    <option value="all">All</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>

                <Button variant="outline" size="sm" onClick={() => {
                  setSearchQuery("")
                  setCategoryFilter("all")
                  setStatusFilter("all")
                }}>
                  <Filter className="mr-2 h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Services List */}
        <div className="space-y-4">
          {filteredServices.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No Services Found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Try adjusting your filters or search query.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredServices.map(service => (
              <Card key={service.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle>{service.name}</CardTitle>
                      <CardDescription className="mt-2">
                        {service.description}
                      </CardDescription>
                    </div>
                    <Badge variant={service.is_active ? "default" : "secondary"}>
                      {service.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Layers className="mr-2 h-4 w-4" />
                        Category: {service.category.name}
                      </div>
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4" />
                        Requirements: {service.requirements_count}
                      </div>
                      <div className="flex items-center">
                        <GitBranch className="mr-2 h-4 w-4" />
                        Workflow Stages: {service.workflow_stages_count}
                      </div>
                      <div className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4" />
                        Processing: {service.processing_days} day{service.processing_days !== 1 ? 's' : ''}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/mswdo-officer/services/${service.id}/edit`}>
                          Edit Service
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/mswdo-officer/services/${service.id}/requirements`}>
                          Manage Requirements
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/mswdo-officer/services/${service.id}/workflow`}>
                          Configure Workflow
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </AppLayout>
  )
} 