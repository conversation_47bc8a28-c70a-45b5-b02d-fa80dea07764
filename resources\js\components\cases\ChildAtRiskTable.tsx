import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';

interface ChildAtRiskCase {
    id: number;
    caseloadNo: string;
    dateOfReferral: string;
    nameOfCAR: string;
    address: string;
    brgy: string;
    address2: string;
    birthdate: string;
    sex: string;
    age: number;
    nameOfGuardian: string;
    addressOfGuardian: string;
    highestEducational: string;
    offenseCommitted: string;
    remarks: string;
}

interface ChildAtRiskTableProps {
    cases: ChildAtRiskCase[];
    onEdit?: (caseItem: ChildAtRiskCase) => void;
    onDelete?: (caseItem: ChildAtRiskCase) => void;
    onView?: (caseItem: ChildAtRiskCase) => void;
}

export default function ChildAtRiskTable({ cases, onEdit, onDelete, onView }: ChildAtRiskTableProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const filteredCases = cases.filter(caseItem =>
        Object.values(caseItem).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;
        
        const aValue = a[sortField as keyof ChildAtRiskCase];
        const bValue = b[sortField as keyof ChildAtRiskCase];
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return (
        <div className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search child at risk cases..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            {/* Table */}
            <div className="border border-gray-800 rounded-lg overflow-hidden">
                <div className="bg-red-600 text-white text-center py-2 font-bold text-sm">
                    MASTERLIST OF CAR<br />
                    MUNICIPALITY OF BALAGTAS<br />
                    2024
                </div>
                
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-100 border-b border-gray-800">
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-bold text-xs">
                                    Caseload no.
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">DATE OF REFERRAL/REF</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">NAME OF CAR</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2 min-w-[100px]">
                                ADDRESS<br />
                                Brgy.
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">ADDRESS</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Birthdate</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">SEX</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">AGE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">NAME OF GUARDIAN</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">ADDRESS OF GUARDIAN</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">HIGHEST EDUCATIONAL</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">OFFENSE COMMITTED</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">REMARKS</TableHead>
                            <TableHead className="text-center font-bold text-xs p-2">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedCases.map((caseItem) => (
                            <TableRow key={caseItem.id} className="hover:bg-gray-50 border-b border-gray-800">
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.caseloadNo}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.dateOfReferral}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.nameOfCAR}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.brgy}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.address2}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.birthdate}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.sex}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.age}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.nameOfGuardian}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.addressOfGuardian}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.highestEducational}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.offenseCommitted}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.remarks}</TableCell>
                                <TableCell className="text-center p-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onView?.(caseItem)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                View
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onEdit?.(caseItem)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onDelete?.(caseItem)} className="text-red-600">
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
