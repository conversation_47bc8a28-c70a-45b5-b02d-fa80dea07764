export interface Permission {
    module: string;
    name: string;
    description: string;
}

export interface User {
    id: string;
    name: string;
    email: string;
    role: "social-worker" | "applicant";
    status: "active" | "inactive" | "suspended";
    lastLogin: string;
    createdAt: string;
    permissions: string[];
}

export const availablePermissions: Permission[] = [
    { module: "Case Management", name: "case_management", description: "Access to case management module" },
    { module: "Residency Verification", name: "residency_verification", description: "Access to residency verification module" },
    { module: "BFACES", name: "bfaces", description: "Access to BFACES application management" },
    { module: "Service Requests", name: "service_requests", description: "Access to service request management" },
    { module: "Interviews", name: "interviews", description: "Access to interview management" },
    { module: "Client Management", name: "client_management", description: "Access to client management" },
    { module: "Reports", name: "reports", description: "Access to reports and analytics" }
];

export const dummyUsers: User[] = [
    {
        id: "1",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "social-worker",
        status: "active",
        lastLogin: "2024-04-08T10:30:00",
        createdAt: "2024-01-01T00:00:00",
        permissions: ["case_management", "residency_verification", "service_requests"]
    },
    {
        id: "2",
        name: "Maria Santos",
        email: "<EMAIL>",
        role: "social-worker",
        status: "active",
        lastLogin: "2024-04-07T15:45:00",
        createdAt: "2024-01-02T00:00:00",
        permissions: ["interviews", "client_management", "reports"]
    },
    {
        id: "3",
        name: "Pedro Reyes",
        email: "<EMAIL>",
        role: "applicant",
        status: "inactive",
        lastLogin: "2024-03-15T09:15:00",
        createdAt: "2024-01-03T00:00:00",
        permissions: []
    }
];

export const stats = {
    totalUsers: 1245,
    roleDistribution: {
        applicant: 856,
        'social-worker': 12,
        'mswdo-officer': 2
    },
    verificationStatus: {
        verified: 878,
        pending: 45,
        rejected: 12
    },
    activityMetrics: {
        activeThisMonth: 756,
        newRegistrations: 89,
        accountDeletions: 3
    }
};