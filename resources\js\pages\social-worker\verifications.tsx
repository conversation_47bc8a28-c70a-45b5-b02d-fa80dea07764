import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FileText, Search, User, Calendar, CheckCircle, XCircle, AlertCircle, Download } from "lucide-react";
import { useState } from "react";
import { type BreadcrumbItem } from "@/types";

interface Verification {
  id: string;
  clientName: string;
  documentType: "residency" | "income" | "medical" | "senior" | "student" | "other";
  status: "pending" | "verified" | "rejected" | "needs-review";
  submittedAt: string;
  verifiedAt?: string;
  verifiedBy?: string;
  notes?: string;
  documentUrl: string;
  urgency: "normal" | "urgent";
}

// Dummy data for demonstration
const dummyVerifications: Verification[] = [
  {
    id: "1",
    clientName: "Juan <PERSON>a Cruz",
    documentType: "residency",
    status: "pending",
    submittedAt: "2024-04-08",
    documentUrl: "/documents/residency-1.pdf",
    urgency: "urgent",
  },
  {
    id: "2",
    clientName: "Maria Santos",
    documentType: "income",
    status: "verified",
    submittedAt: "2024-04-07",
    verifiedAt: "2024-04-08",
    verifiedBy: "Admin User",
    notes: "All requirements complete and verified",
    documentUrl: "/documents/income-2.pdf",
    urgency: "normal",
  },
  {
    id: "3",
    clientName: "Pedro Reyes",
    documentType: "medical",
    status: "needs-review",
    submittedAt: "2024-04-07",
    notes: "Additional documentation needed",
    documentUrl: "/documents/medical-3.pdf",
    urgency: "urgent",
  },
];

export default function Verifications() {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [urgencyFilter, setUrgencyFilter] = useState<string>("all");

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/admin/dashboard" },
    { title: "Document Verifications", href: "/admin/verifications" },
  ];

  const filteredVerifications = dummyVerifications.filter(verification => {
    const matchesStatus = statusFilter === "all" || verification.status === statusFilter;
    const matchesType = typeFilter === "all" || verification.documentType === typeFilter;
    const matchesUrgency = urgencyFilter === "all" || verification.urgency === urgencyFilter;
    const matchesSearch = verification.clientName.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesType && matchesUrgency && matchesSearch;
  });

  const getStatusBadge = (status: Verification["status"]) => {
    const variants: Record<Verification["status"], "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      verified: "secondary",
      rejected: "destructive",
      "needs-review": "default",
    };
    return <Badge variant={variants[status]}>{status}</Badge>;
  };

  const getUrgencyBadge = (urgency: Verification["urgency"]) => {
    return (
      <Badge variant={urgency === "urgent" ? "destructive" : "outline"}>
        {urgency}
      </Badge>
    );
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Document Verifications" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Document Verifications</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage and verify submitted documents
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export List
            </Button>
            <Button>View Guidelines</Button>
          </div>
        </div>

        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by client name..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="needs-review">Needs Review</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type">Document Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="residency">Residency</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="medical">Medical</SelectItem>
                  <SelectItem value="senior">Senior Citizen</SelectItem>
                  <SelectItem value="student">Student</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="urgency">Urgency</Label>
              <Select value={urgencyFilter} onValueChange={setUrgencyFilter}>
                <SelectTrigger id="urgency">
                  <SelectValue placeholder="Filter by urgency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <div className="space-y-4">
          {filteredVerifications.map((verification) => (
            <Card key={verification.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex gap-4">
                  <div className="mt-1">
                    <FileText className="h-5 w-5 text-gray-400" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{verification.clientName}</h3>
                      {getStatusBadge(verification.status)}
                      {getUrgencyBadge(verification.urgency)}
                    </div>
                    <div className="mt-2 space-y-1 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="capitalize">{verification.documentType} Document</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Submitted: {verification.submittedAt}</span>
                      </div>
                      {verification.verifiedAt && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          <span>Verified: {verification.verifiedAt} by {verification.verifiedBy}</span>
                        </div>
                      )}
                      {verification.notes && (
                        <p className="mt-2 text-sm text-gray-600">{verification.notes}</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" asChild>
                    <a href={verification.documentUrl} target="_blank" rel="noopener noreferrer">
                      View Document
                    </a>
                  </Button>
                  {verification.status === "pending" && (
                    <>
                      <Button size="sm" variant="outline">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Verify
                      </Button>
                      <Button size="sm" variant="outline">
                        <XCircle className="h-4 w-4 mr-2" />
                        Reject
                      </Button>
                    </>
                  )}
                  {verification.status === "needs-review" && (
                    <Button size="sm">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Review
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}

          {filteredVerifications.length === 0 && (
            <Card className="p-8">
              <div className="text-center text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-semibold">No documents found</h3>
                <p>There are no documents matching your filters.</p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </AppLayout>
  );
} 