export interface Application {
    id: string;
    clientId: string;
    clientName: string;
    service: string;
    status: "pending" | "in_progress" | "approved" | "rejected";
    priority: "low" | "medium" | "high";
    submittedAt: string;
    lastUpdated: string;
    assignedTo?: string;
    notes?: string;
}

export interface ScheduleItem {
    id: string;
    clientId: string;
    clientName: string;
    purpose: string;
    date: string;
    time: string;
    location?: string;
    status: "scheduled" | "in_progress" | "completed" | "cancelled";
    assignedTo?: string;
    notes?: string;
}

export interface DashboardData {
    totalCases: number;
    activeCases: number;
    pendingVerifications: number;
    urgentCases: number;
    todayAppointments: number;
    pendingApplications: number;
    resolvedToday: number;
    recentActivity: Array<{
        type: string;
        description: string;
        client: string;
        time: string;
    }>;
}

export interface Client {
    id: string;
    name: string;
    email: string;
    phone: string;
    barangay: string;
    address: string;
    status: "verified" | "pending" | "inactive" | "blocked";
    joinedDate: string;
    lastActive: string;
    applications: number;
    servicesReceived: number;
    pendingVerifications: number;
    bfacesStatus: "verified" | "pending" | "none";
}

export interface Verification {
    id: string;
    clientName: string;
    documentType: "residency" | "income" | "medical" | "senior" | "student" | "other";
    status: "pending" | "verified" | "rejected" | "needs-review";
    submittedAt: string;
    verifiedAt?: string;
    verifiedBy?: string;
    notes?: string;
    documentUrl: string;
    urgency: "normal" | "urgent";
}

export interface Interview {
    id: string;
    client: string;
    type: string;
    date: string;
    time: string;
    status: 'scheduled' | 'completed' | 'cancelled';
}

export interface Service {
    id: number;
    title: string;
    description: string;
    eligibility: string[];
    requirements: string[];
    process: string[];
    status: "available" | "limited" | "unavailable";
    schedule: string;
    location: string;
    maxBeneficiaries?: number;
    currentBeneficiaries?: number;
    applications: {
        id: number;
        client: {
            id: number;
            name: string;
        };
        status: "pending" | "approved" | "rejected";
        appliedAt: string;
    }[];
    createdAt: string;
    updatedAt: string;
}

export interface Case {
    id: number;
    client: {
        id: number;
        name: string;
        email: string;
        phone?: string;
        barangay?: string;
        address?: string;
    };
    type: "bfaces" | "service" | "general";
    status: "new" | "in_progress" | "pending_review" | "resolved" | "closed";
    priority: "low" | "medium" | "high" | "urgent";
    assignedAt: string;
    lastUpdated: string;
    nextFollowUp?: string;
    description: string;
    notes?: string[];
}

export interface Report {
    id: string;
    title: string;
    type: string;
    period: string;
    status: string;
    generatedAt: string;
}

export interface Appointment {
    id: string;
    clientName: string;
    purpose: string;
    date: string;
    time: string;
    location: string;
    status: "scheduled" | "completed" | "cancelled" | "no-show";
    type: "bfaces" | "senior-citizen" | "medical" | "educational";
    notes: string;
    documents: string[];
    recommendation?: "approve" | "reject" | "pending";
}

export interface BFACESApplication {
    id: number;
    client: {
        id: number;
        name: string;
        email: string;
        phone?: string;
        barangay?: string;
        address?: string;
    };
    status: "submitted" | "under_review" | "approved" | "rejected";
    submittedAt: string;
    reviewedAt?: string;
    reviewedBy?: {
        id: number;
        name: string;
    };
    rejectionReason?: string;
    crisisType: string;
    crisisDescription: string;
    assistanceNeeded: string;
    monthlyIncome: number;
    familyMembers: {
        name: string;
        age: string;
        relationship: string;
        occupation: string;
    }[];
    documents: {
        id: number;
        name: string;
        type: string;
        url: string;
        verified: boolean;
    }[];
    notes?: string[];
}

export interface ServiceStats {
    totalApplications: number;
    approvedApplications: number;
    pendingApplications: number;
    rejectedApplications: number;
    serviceTypes: Array<{
        name: string;
        count: number;
    }>;
    monthlyApplications: Array<{
        month: string;
        count: number;
    }>;
}

export interface CaseStats {
    totalCases: number;
    resolvedCases: number;
    pendingCases: number;
    urgentCases: number;
    caseTypes: Array<{
        type: string;
        count: number;
    }>;
    resolutionTimes: Array<{
        range: string;
        count: number;
    }>;
}

export interface AuthUser {
    id: string;
    name: string;
    email: string;
    permissions: string[];
} 