import { Head, useForm, <PERSON> } from '@inertiajs/react';
import { User2, Mail, Phone, MapPin, Building2, Lock, Eye, EyeOff, LoaderCircle, ArrowLeft, Users, UserCheck, Shield } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    address: string;
    barangay: string;
    contact_number: string;
    family_role: 'head_of_family';
    bfaces_control_code?: string;
};

export default function Register() {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm<RegisterForm>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        address: '',
        barangay: '',
        contact_number: '',
        family_role: 'head_of_family',
        bfaces_control_code: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    const barangays = [
        'Borol 1st', 'Borol 2nd', 'Dalig', 'Longos', 'Panginay', 'Pulong Gubat', 'San Juan', 
        'Santol', 'Wawa'
    ];

    return (
        <AuthLayout title="Create an account" description="Register for Balagtas SocialCare services">
            <Head title="Register" />

            <div className="flex flex-col items-center max-w-md mx-auto w-full">
                {/* Back to Home Link */}
                <div className="w-full mb-6">
                    <Link
                        href={route('home')}
                        className="inline-flex items-center gap-2 text-purple-600 hover:text-purple-800 font-medium text-sm transition-colors duration-200 group"
                    >
                        <ArrowLeft className="h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
                        Back to Main Site
                    </Link>
                </div>

                {/* Registration Info */}
                <div className="w-full mb-6">
                    <div className="border-2 border-purple-200 rounded-lg p-4 bg-purple-50">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                                <h3 className="font-semibold text-purple-900">Head of Family Registration</h3>
                                <p className="text-sm text-purple-600">
                                    Only heads of family can register. You will be able to add family members during the BFACES form process.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <form className="w-full space-y-6" onSubmit={submit}>
                    <div className="space-y-6">
                        <div>
                            <Label htmlFor="name" className="text-purple-900 mb-1.5 block">Full Name</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <User2 className="h-5 w-5 text-purple-400" />
                                </div>
                                <Input
                                    id="name"
                                    type="text"
                                    required
                                    autoFocus
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg h-12 transition-colors duration-200"
                                    placeholder="Juan Dela Cruz"
                                />
                            </div>
                            <InputError message={errors.name} />
                        </div>

                        <div>
                            <Label htmlFor="address" className="text-purple-900 mb-1.5 block">Street Address</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <MapPin className="h-5 w-5 text-purple-400" />
                                </div>
                                <Input
                                    id="address"
                                    type="text"
                                    required
                                    value={data.address}
                                    onChange={(e) => setData('address', e.target.value)}
                                    className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                                    placeholder="123 Main Street"
                                />
                            </div>
                            <InputError message={errors.address} />
                        </div>

                        <div>
                            <Label htmlFor="email" className="text-purple-900 mb-1.5 block">Email Address</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <Mail className="h-5 w-5 text-purple-400" />
                                </div>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            <InputError message={errors.email} />
                        </div>

                        <div>
                            <Label htmlFor="barangay" className="text-purple-900 mb-1.5 block">Barangay</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <Building2 className="h-5 w-5 text-purple-400" />
                                </div>
                                <Select 
                                    defaultValue={data.barangay} 
                                    onValueChange={(value) => setData('barangay', value)}
                                >
                                    <SelectTrigger className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg">
                                        <SelectValue placeholder="Select your barangay" />
                                    </SelectTrigger>
                                    <SelectContent className="border-purple-300 hover:border-purple-400">
                                        {barangays.map(barangay => (
                                            <SelectItem key={barangay} value={barangay}>{barangay}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <InputError message={errors.barangay} />
                        </div>

                        <div>
                            <Label htmlFor="contact_number" className="text-purple-900 mb-1.5 block">Contact Number</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center justify-center pointer-events-none z-10">
                                    <Phone className="h-5 w-5 text-purple-400 flex-shrink-0" />
                                </div>
                                <Input
                                    id="contact_number"
                                    type="tel"
                                    required
                                    value={data.contact_number}
                                    onChange={(e) => {
                                        // Only allow numbers and limit to 11 characters
                                        const value = e.target.value.replace(/\D/g, '').slice(0, 11);
                                        setData('contact_number', value);
                                    }}
                                    className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-colors duration-200"
                                    placeholder="09XX XXX XXXX"
                                    maxLength={11}
                                    pattern="09[0-9]{9}"
                                    inputMode="numeric"
                                    title="Please enter a valid phone number starting with 09"
                                    aria-describedby="contact-help"
                                />
                                <div id="contact-help" className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                                    <span className="inline-block w-1 h-1 bg-purple-400 rounded-full"></span>
                                    Enter your 11-digit mobile number starting with 09
                                </div>
                            </div>
                            <InputError message={errors.contact_number} />
                        </div>

                        <div>
                            <Label htmlFor="password" className="text-purple-900 mb-1.5 block">Password</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <Lock className="h-5 w-5 text-purple-400" />
                                </div>
                                <Input
                                    id="password"
                                    type={showPassword ? "text" : "password"}
                                    required
                                    value={data.password}
                                    onChange={(e) => setData('password', e.target.value)}
                                    className="pl-10 pr-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowPassword(!showPassword)}
                                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                >
                                    {showPassword ? (
                                        <EyeOff className="h-5 w-5 text-purple-400" />
                                    ) : (
                                        <Eye className="h-5 w-5 text-purple-400" />
                                    )}
                                </button>
                            </div>
                            <InputError message={errors.password} />
                        </div>

                        <div>
                            <Label htmlFor="password_confirmation" className="text-purple-900 mb-1.5 block">Confirm Password</Label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <Lock className="h-5 w-5 text-purple-400" />
                                </div>
                                <Input
                                    id="password_confirmation"
                                    type={showConfirmPassword ? "text" : "password"}
                                    required
                                    value={data.password_confirmation}
                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                    className="pl-10 pr-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                >
                                    {showConfirmPassword ? (
                                        <EyeOff className="h-5 w-5 text-purple-400" />
                                    ) : (
                                        <Eye className="h-5 w-5 text-purple-400" />
                                    )}
                                </button>
                            </div>
                            <InputError message={errors.password_confirmation} />
                        </div>
                    </div>

                    <Button 
                        type="submit"
                        className="w-full bg-purple-600 text-white hover:bg-purple-700 font-medium py-2.5 rounded-lg cursor-pointer transition-all duration-300 ease-in-out"
                        disabled={processing}
                    >
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                        Register
                    </Button>

                    <div className="text-center text-sm text-purple-600">
                        Already have an account?{' '}
                        <TextLink href={route('login')} className="text-purple-700 hover:text-purple-800 font-medium">
                            Log in
                        </TextLink>
                    </div>
                </form>
            </div>
        </AuthLayout>
    );
}
