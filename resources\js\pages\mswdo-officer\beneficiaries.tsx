import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Filter, Users, UserCheck, UserX, Eye, Edit, FileText, CheckCircle } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Overview',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'BFACES Management',
        href: '#',
    }
];

// Sample beneficiary data
const beneficiaries = [
    {
        id: 1,
        name: '<PERSON>',
        age: 45,
        address: 'Barangay San Jose, Balagtas',
        category: 'Senior Citizen',
        status: 'active',
        services: ['Medical Assistance', 'Social Pension'],
        lastAssistance: '2024-03-15',
        totalAssistance: 25000,
        contactNumber: '09123456789'
    },
    {
        id: 2,
        name: '<PERSON>',
        age: 35,
        address: 'Barangay Poblacion, Balagtas',
        category: 'PWD',
        status: 'active',
        services: ['Assistive Devices', 'Skills Training'],
        lastAssistance: '2024-03-10',
        totalAssistance: 15000,
        contactNumber: '09987654321'
    },
    {
        id: 3,
        name: 'Ana Rodriguez',
        age: 28,
        address: 'Barangay Longos, Balagtas',
        category: 'Solo Parent',
        status: 'pending',
        services: ['Educational Support'],
        lastAssistance: '2024-02-20',
        totalAssistance: 8000,
        contactNumber: '09456789123'
    }
];

const stats = {
    totalBeneficiaries: 1247,
    activeBeneficiaries: 1089,
    pendingApplications: 158,
    seniorCitizens: 456,
    pwd: 234,
    soloParents: 189,
    children: 368
};

export default function BeneficiaryManagement() {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
            case 'pending':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case 'inactive':
                return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Beneficiary Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">BFACES Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Barangay Family Assessment and Case Evaluation System</p>
                    </div>
                    <div className="flex gap-2">
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                            <Link href="/mswdo-officer/bfaces/heads/new">
                                <Plus className="h-4 w-4 mr-2" />
                                New BFACES Registration
                            </Link>
                        </Button>
                        <Button asChild variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Link href="/mswdo-officer/bfaces/verification">
                                <FileText className="h-4 w-4 mr-2" />
                                Document Verification
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* BFACES Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Registered Families</CardTitle>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Users className="h-5 w-5 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">247</div>
                            <CardDescription className="text-purple-600">
                                189 approved families
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Heads of Family</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">247</div>
                            <CardDescription className="text-green-600">
                                189 with BFACES codes
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Family Members</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">892</div>
                            <CardDescription className="text-blue-600">
                                Linked family members
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Verification</CardTitle>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <UserX className="h-5 w-5 text-orange-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">58</div>
                            <CardDescription className="text-orange-600">
                                Documents under review
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search families, control codes, or names..."
                            className="pl-10"
                        />
                    </div>
                    <Button variant="outline" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filter
                    </Button>
                </div>

                {/* BFACES Registry */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Users className="h-5 w-5 mr-2" />
                            BFACES Family Registry
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Verified families with completed BFACES applications and active control codes
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                        <div className="grid gap-4">
                            {/* Mock BFACES Family Data */}
                            {[
                                {
                                    id: 1,
                                    headOfFamily: "Maria Santos",
                                    bfacesCode: "BFC000123456",
                                    familyMembers: 4,
                                    barangay: "San Juan",
                                    status: "approved",
                                    documentStatus: "verified",
                                    registrationDate: "2024-01-15",
                                    lastUpdate: "2024-03-20"
                                },
                                {
                                    id: 2,
                                    headOfFamily: "Juan Dela Cruz",
                                    bfacesCode: "BFC000789012",
                                    familyMembers: 6,
                                    barangay: "Wawa",
                                    status: "under_review",
                                    documentStatus: "pending",
                                    registrationDate: "2024-02-10",
                                    lastUpdate: "2024-03-18"
                                },
                                {
                                    id: 3,
                                    headOfFamily: "Ana Rodriguez",
                                    bfacesCode: "BFC000345678",
                                    familyMembers: 3,
                                    barangay: "Panginay",
                                    status: "approved",
                                    documentStatus: "verified",
                                    registrationDate: "2024-01-08",
                                    lastUpdate: "2024-03-15"
                                }
                            ].map((family) => (
                                <Card key={family.id} className="border-purple-200 hover:bg-purple-50/30 transition-colors">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <h3 className="font-semibold text-lg text-purple-900">{family.headOfFamily}</h3>
                                                    <Badge variant={family.status === 'approved' ? 'default' : family.status === 'under_review' ? 'secondary' : 'destructive'}>
                                                        {family.status.replace('_', ' ')}
                                                    </Badge>
                                                    <Badge variant="outline" className="text-xs font-mono">
                                                        {family.bfacesCode}
                                                    </Badge>
                                                </div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                                                    <p><span className="font-medium">Family Members:</span> {family.familyMembers}</p>
                                                    <p><span className="font-medium">Barangay:</span> {family.barangay}</p>
                                                    <p><span className="font-medium">Document Status:</span>
                                                        <Badge variant={family.documentStatus === 'verified' ? 'default' : 'secondary'} className="ml-2 text-xs">
                                                            {family.documentStatus}
                                                        </Badge>
                                                    </p>
                                                    <p><span className="font-medium">Last Updated:</span> {family.lastUpdate}</p>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Registered:</span> {family.registrationDate}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/bfaces/${family.id}`}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View Family
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/bfaces/${family.id}/edit`}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/bfaces/${family.id}/verification`}>
                                                        <FileText className="h-4 w-4 mr-2" />
                                                        Documents
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
