import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import DeleteUser from '@/components/delete-user';
import SettingsLayout from '@/layouts/settings-layout';
import { User2, Mail, Phone, MapPin, Building2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Settings',
        href: '/client/settings/profile',
    },
    {
        title: 'Profile',
        href: '/client/settings/profile',
    },
];

const barangays = [
    '<PERSON><PERSON> 1st', '<PERSON><PERSON> 2nd', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Panginay', '<PERSON>ulong Gubat', '<PERSON> Juan', 
    'Santo<PERSON>', 'Wawa'
];

interface Props {
  user: {
    name: string
    email: string
    phone?: string
    barangay?: string
    address?: string
  }
}

export default function ClientProfile({ user }: Props) {
  if (!user) {
    return null;
  }

  const { data, setData, patch, errors, processing } = useForm({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    barangay: user?.barangay || "",
    address: user?.address || "",
  });

  function submit(e: React.FormEvent) {
    e.preventDefault();
    patch('/client/settings/profile');
  }

  return (
    <SettingsLayout breadcrumbs={breadcrumbs} role="client" current="profile">
      <Head title="Profile Settings" />

      <Card>
        <form onSubmit={submit}>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>
              Update your personal information and contact details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="name" className="text-purple-900 mb-1.5 block">Full Name</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User2 className="h-5 w-5 text-purple-400" />
                </div>
                <Input
                  id="name"
                  type="text"
                  value={data.name}
                  onChange={e => setData("name", e.target.value)}
                  className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                  placeholder="Juan Dela Cruz"
                />
              </div>
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email" className="text-purple-900 mb-1.5 block">Email Address</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-purple-400" />
                </div>
                <Input
                  id="email"
                  type="email"
                  value={data.email}
                  onChange={e => setData("email", e.target.value)}
                  className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>

            <div>
              <Label htmlFor="phone" className="text-purple-900 mb-1.5 block">Contact Number</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-purple-400" />
                </div>
                <Input
                  id="phone"
                  type="tel"
                  value={data.phone}
                  onChange={e => setData("phone", e.target.value)}
                  className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                  placeholder="09XX XXX XXXX"
                />
              </div>
              {errors.phone && (
                <p className="text-sm text-red-500">{errors.phone}</p>
              )}
            </div>

            <div>
              <Label htmlFor="barangay" className="text-purple-900 mb-1.5 block">Barangay</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                  <Building2 className="h-5 w-5 text-purple-400" />
                </div>
                <Select 
                  value={data.barangay} 
                  onValueChange={(value) => setData('barangay', value)}
                >
                  <SelectTrigger className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg">
                    <SelectValue placeholder="Select your barangay" />
                  </SelectTrigger>
                  <SelectContent className="border-purple-300 hover:border-purple-400">
                    {barangays.map(barangay => (
                      <SelectItem key={barangay} value={barangay}>{barangay}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {errors.barangay && (
                <p className="text-sm text-red-500">{errors.barangay}</p>
              )}
            </div>

            <div>
              <Label htmlFor="address" className="text-purple-900 mb-1.5 block">Street Address</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MapPin className="h-5 w-5 text-purple-400" />
                </div>
                <Input
                  id="address"
                  type="text"
                  value={data.address}
                  onChange={e => setData("address", e.target.value)}
                  className="pl-10 bg-white border-purple-300 focus:border-purple-500 focus:ring-purple-500 rounded-lg"
                  placeholder="123 Main Street"
                />
              </div>
              {errors.address && (
                <p className="text-sm text-red-500">{errors.address}</p>
              )}
            </div>

            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={processing}
                className="bg-purple-600 text-white hover:bg-purple-700 font-medium py-2.5 rounded-lg cursor-pointer transition-all duration-300 ease-in-out"
              >
                Save Changes
              </Button>
            </div>
          </CardContent>
        </form>
      </Card>

      <div className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Delete Account</CardTitle>
            <CardDescription>
              Permanently delete your account and all associated data.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DeleteUser />
          </CardContent>
        </Card>
      </div>
    </SettingsLayout>
  );
} 