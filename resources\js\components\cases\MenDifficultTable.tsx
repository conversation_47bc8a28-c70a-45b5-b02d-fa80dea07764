import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';

interface MenDifficultCase {
    id: number;
    caseloadNo: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthdate: string;
    barangay: string;
    address: string;
    municipality: string;
    province: string;
    educational: string;
    attainment: string;
    previousOffense: string;
    typeOfOffense: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string;
    referral: string;
    other: string;
    status: string;
}

interface MenDifficultTableProps {
    cases: MenDifficultCase[];
    onEdit?: (caseItem: MenDifficultCase) => void;
    onDelete?: (caseItem: MenDifficultCase) => void;
    onView?: (caseItem: MenDifficultCase) => void;
}

export default function MenDifficultTable({ cases, onEdit, onDelete, onView }: MenDifficultTableProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const filteredCases = cases.filter(caseItem =>
        Object.values(caseItem).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;
        
        const aValue = a[sortField as keyof MenDifficultCase];
        const bValue = b[sortField as keyof MenDifficultCase];
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return (
        <div className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search men in difficult circumstances cases..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            {/* Table */}
            <div className="border border-gray-800 rounded-lg overflow-hidden">
                <div className="bg-red-600 text-white text-center py-2 font-bold text-sm">
                    Inventory of Cases of Men in Especially Difficult Circumstances<br />
                    Municipality of Balagtas
                </div>
                
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-100 border-b border-gray-800">
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">
                                <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-bold text-xs">
                                    Caseload No.
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Date</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Name of Client (Fullname)</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Age</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Sex</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">BIRTHDATE</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Barangay</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Address Municipality</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Province</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Educational Attainment</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Previous Offense</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Type of Offense</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Source of Referral</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Method</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Initial Assessment</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Intervention Assistance</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Final Evaluation</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Termination Assistance</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Referral</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Other</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-1">Status</TableHead>
                            <TableHead className="text-center font-bold text-xs p-1">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedCases.map((caseItem) => (
                            <TableRow key={caseItem.id} className="hover:bg-gray-50 border-b border-gray-800">
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.caseloadNo}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.date}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.nameOfClient}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.age}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.sex}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.birthdate}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.barangay}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.address}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.province}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.educational}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.previousOffense}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.typeOfOffense}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.sourceOfReferral}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.method}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.initial}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.intervention}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.final}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.termination}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.referral}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.other}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-1">{caseItem.status}</TableCell>
                                <TableCell className="text-center p-1">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-6 w-6 p-0">
                                                <MoreHorizontal className="h-3 w-3" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onView?.(caseItem)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                View
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onEdit?.(caseItem)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onDelete?.(caseItem)} className="text-red-600">
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
