<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bfaces_forms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('head_of_family_id')->constrained('users')->onDelete('cascade');
            $table->string('bfaces_control_code', 12)->unique();

            // Household Information
            $table->json('household_composition'); // Array of family members with details
            $table->decimal('total_monthly_income', 10, 2)->nullable();
            $table->json('income_sources')->nullable(); // Array of income source details
            $table->integer('total_household_members')->default(1);

            // Housing Information
            $table->enum('housing_type', [
                'owned', 'rented', 'shared', 'informal_settler', 'government_housing', 'other'
            ])->nullable();
            $table->text('housing_description')->nullable();
            $table->boolean('has_electricity')->default(false);
            $table->boolean('has_water_supply')->default(false);
            $table->boolean('has_toilet_facility')->default(false);

            // Socio-Economic Information
            $table->json('vulnerabilities')->nullable(); // PWD, Senior Citizen, Solo Parent, etc.
            $table->json('current_assistance_received')->nullable(); // Other government programs
            $table->text('special_circumstances')->nullable();

            // Assessment Information
            $table->enum('poverty_level', ['extremely_poor', 'poor', 'near_poor', 'not_poor'])->nullable();
            $table->decimal('per_capita_income', 10, 2)->nullable();
            $table->json('priority_needs')->nullable(); // Health, Education, Livelihood, etc.

            // Form Status and Tracking
            $table->enum('form_status', [
                'draft', 'submitted', 'under_review', 'approved', 'rejected', 'requires_revision'
            ])->default('draft');

            $table->foreignId('assessed_by')->nullable()->constrained('users'); // Social worker
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('assessed_at')->nullable();
            $table->text('assessment_notes')->nullable();

            // Approval and Control Code Generation
            $table->foreignId('approved_by')->nullable()->constrained('users'); // MSWDO Officer
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('control_code_generated_at')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['bfaces_control_code']);
            $table->index(['form_status']);
            $table->index(['head_of_family_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bfaces_forms');
    }
};
