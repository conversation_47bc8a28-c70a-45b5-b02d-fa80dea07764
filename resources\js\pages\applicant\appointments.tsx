import { <PERSON>, <PERSON> } from "@inertiajs/react"
import AppLayout from "@/layouts/app/app-sidebar-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { type BreadcrumbItem } from "@/types"
import Heading from "@/components/heading"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, Plus, User2 } from "lucide-react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { dummyAppointments } from "@/data/dummy-data"

interface Appointment {
  id: number
  socialWorker: {
    id: number
    name: string
    email: string
  }
  date: string
  time: string
  location: string
  purpose: string
  status: "scheduled" | "completed" | "cancelled" | "rescheduled"
  notes?: string
  rescheduledTo?: {
    date: string
    time: string
  }
}

interface Props {
  upcomingAppointments: Appointment[]
  pastAppointments: Appointment[]
}

export default function Appointments({ 
  upcomingAppointments = dummyAppointments.upcoming, 
  pastAppointments = dummyAppointments.past 
}: Props) {
  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/client/dashboard" },
    { title: "Appointments", href: "/client/appointments" },
  ]

  function getStatusColor(status: Appointment["status"]) {
    switch (status) {
      case "scheduled":
        return "bg-purple-100 text-purple-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      case "rescheduled":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  function formatDateTime(date: string, time: string) {
    const formattedDate = new Date(date).toLocaleDateString()
    return `${formattedDate} at ${time}`
  }

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Appointments" />

      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <Heading 
            title="Appointments" 
            description="View schedule appointments by social workers." 
          />
        </div>

        <Tabs defaultValue="upcoming" className="w-full">
          <TabsList className="bg-purple-50">
            <TabsTrigger value="upcoming" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              Upcoming ({upcomingAppointments.length})
            </TabsTrigger>
            <TabsTrigger value="past" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              Past ({pastAppointments.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="mt-6">
            <div className="space-y-6">
              {upcomingAppointments.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                    <Calendar className="h-12 w-12 text-purple-400" />
                    <h3 className="mt-4 text-lg font-medium">No Upcoming Appointments</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You don't have any upcoming appointments scheduled.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                upcomingAppointments.map(appointment => (
                  <AppointmentCard 
                    key={appointment.id} 
                    appointment={appointment} 
                    formatDateTime={formatDateTime}
                    getStatusColor={getStatusColor}
                  />
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="past" className="mt-6">
            <div className="space-y-6">
              {pastAppointments.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                    <Calendar className="h-12 w-12 text-purple-400" />
                    <h3 className="mt-4 text-lg font-medium">No Past Appointments</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You haven't had any appointments yet.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                pastAppointments.map(appointment => (
                  <AppointmentCard 
                    key={appointment.id} 
                    appointment={appointment} 
                    formatDateTime={formatDateTime}
                    getStatusColor={getStatusColor}
                  />
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}

interface AppointmentCardProps {
  appointment: Appointment;
  formatDateTime: (date: string, time: string) => string;
  getStatusColor: (status: Appointment["status"]) => string;
}

function AppointmentCard({ appointment, formatDateTime, getStatusColor }: AppointmentCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle>{appointment.purpose}</CardTitle>
            <CardDescription className="mt-2">
              {formatDateTime(appointment.date, appointment.time)}
            </CardDescription>
          </div>
          <Badge className={getStatusColor(appointment.status)}>
            {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col gap-3">
            <div className="flex items-center text-sm text-muted-foreground">
              <User2 className="mr-2 h-4 w-4" />
              Social Worker: {appointment.socialWorker.name}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <Clock className="mr-2 h-4 w-4" />
              Time: {appointment.time}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <MapPin className="mr-2 h-4 w-4" />
              Location: {appointment.location}
            </div>
          </div>

          {appointment.notes && (
            <div className="rounded-md bg-muted p-4">
              <p className="text-sm">{appointment.notes}</p>
            </div>
          )}

          {appointment.status === "rescheduled" && appointment.rescheduledTo && (
            <div className="rounded-md border p-4">
              <p className="text-sm font-medium">Rescheduled to:</p>
              <p className="mt-1 text-sm text-muted-foreground">
                {formatDateTime(appointment.rescheduledTo.date, appointment.rescheduledTo.time)}
              </p>
            </div>
          )}

          {appointment.status === "scheduled" && (
            <div className="flex items-center justify-end gap-4">
              <Button variant="outline" asChild>
                <Link href={`/client/appointments/${appointment.id}/reschedule`}>
                  Reschedule
                </Link>
              </Button>
              <Button variant="destructive" asChild>
                <Link href={`/client/appointments/${appointment.id}/cancel`}>
                  Cancel
                </Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}