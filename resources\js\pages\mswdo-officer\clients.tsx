import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Users, Search, Filter, Mail, Phone, MapPin, Calendar, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Administration',
        href: '#',
    },
    {
        title: 'Registered Applicants/Clients',
        href: '/mswdo-officer/clients',
    },
];

export default function RegisteredClients() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Registered Applicants/Clients" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Registered Applicants/Clients</h1>
                        <p className="text-purple-600 mt-2">All registered users: Unverified (no residency verification), Verified (residency verified), BFACES Complete (full application submitted)</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Export List
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Registered</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">2,847</div>
                            <p className="text-xs text-purple-600">Head of families</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Verified</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">1,856</div>
                            <p className="text-xs text-green-600">Residency verified</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">BFACES Complete</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">1,456</div>
                            <p className="text-xs text-blue-600">Full applications</p>
                        </CardContent>
                    </Card>

                    <Card className="border-red-200 bg-gradient-to-br from-red-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-red-900">Unverified</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-900">535</div>
                            <p className="text-xs text-red-600">No verification</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">This Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">235</div>
                            <p className="text-xs text-blue-600">New registrations</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search Clients
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="md:col-span-2">
                                <Input 
                                    placeholder="Search by name, email, or BFACES control code..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Status</option>
                                    <option value="unverified">Unverified (No residency verification)</option>
                                    <option value="verified">Verified (Residency verified)</option>
                                    <option value="bfaces-complete">BFACES Complete (Full application)</option>
                                </select>
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Barangays</option>
                                    <option value="poblacion">Poblacion</option>
                                    <option value="bagong-nayon">Bagong Nayon</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Client List */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Users className="h-5 w-5 mr-2" />
                            Client Directory
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Registered heads of family and their verification status
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Sample Client Entries */}
                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Maria Santos</h4>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Mail className="h-3 w-3" />
                                                    <EMAIL>
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <Phone className="h-3 w-3" />
                                                    +63 ************
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    Poblacion
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            BFACES Complete
                                        </Badge>
                                        <span className="text-sm text-gray-500">BFC000123456</span>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Profile
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-orange-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Juan Dela Cruz</h4>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Mail className="h-3 w-3" />
                                                    <EMAIL>
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <Phone className="h-3 w-3" />
                                                    +63 ************
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    Bagong Nayon
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-green-100 text-green-800 border-green-200">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Verified
                                        </Badge>
                                        <span className="text-sm text-gray-500">—</span>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Profile
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-red-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Ana Rodriguez</h4>
                                            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Mail className="h-3 w-3" />
                                                    <EMAIL>
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <Phone className="h-3 w-3" />
                                                    +63 ************
                                                </span>
                                                <span className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    San Jose
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-red-100 text-red-800 border-red-200">
                                            <AlertTriangle className="h-3 w-3 mr-1" />
                                            Unverified
                                        </Badge>
                                        <span className="text-sm text-gray-500">—</span>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Profile
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between pt-6 border-t border-purple-100">
                            <p className="text-sm text-gray-600">Showing 1-3 of 2,847 clients</p>
                            <div className="flex gap-2">
                                <Button size="sm" variant="outline" disabled>Previous</Button>
                                <Button size="sm" variant="outline">Next</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
