<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status',
        'phone',
        'family_role',
        'bfaces_control_code',
        'head_of_family_id',
        'family_relationship',
        'document_verification_status',
        'bfaces_status',
        'documents_submitted_at',
        'documents_verified_at',
        'bfaces_form_completed_at',
        'bfaces_approved_at',
        'verified_by',
        'verification_notes'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'documents_submitted_at' => 'datetime',
            'documents_verified_at' => 'datetime',
            'bfaces_form_completed_at' => 'datetime',
            'bfaces_approved_at' => 'datetime',
        ];
    }

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function adminProfile()
    {
        return $this->hasOne(AdminProfile::class);
    }

    // BFACES Relationships
    public function bfacesForm()
    {
        return $this->hasOne(BfacesForm::class, 'head_of_family_id');
    }

    public function bfacesDocuments()
    {
        return $this->hasMany(BfacesDocument::class);
    }



    public function verifiedDocuments()
    {
        return $this->hasMany(BfacesDocument::class, 'reviewed_by');
    }

    public function verifiedUsers()
    {
        return $this->hasMany(User::class, 'verified_by');
    }

    public function isSocialWorker()
    {
        return $this->role === 'social-worker';
    }

    public function isMswdoOfficer()
    {
        return $this->role === 'mswdo-officer';
    }

    public function isApplicant()
    {
        return $this->role === 'applicant';
    }

    public function isVerified()
    {
        return $this->status === 'verified';
    }

    public function hasRole(string $role): bool
    {
        // Normalize the user's role to standard format
        $userRole = $this->normalizeRole($this->role);

        // Superadmin and admin have access to all roles
        if (in_array($userRole, ['superadmin', 'mswdo-officer'])) {
            return true;
        }

        if ($role === 'social-worker') {
            return in_array($userRole, ['social-worker', 'mswdo-officer']);
        }

        return $userRole === $role;
    }

    /**
     * Normalize role names to standard format
     */
    private function normalizeRole(?string $role): string
    {
        if (!$role) {
            return 'applicant';
        }

        // Map various role formats to standard names
        $roleMap = [
            'superadmin' => 'mswdo-officer',
            'admin' => 'mswdo-officer',
            'mswdo-officer' => 'mswdo-officer',
            'MSWDO Officer' => 'mswdo-officer',
            'social-worker' => 'social-worker',
            'Social Worker' => 'social-worker',
            'applicant' => 'applicant',
            'client' => 'applicant',
            'Applicant' => 'applicant',
        ];

        return $roleMap[trim($role)] ?? 'applicant';
    }

    /**
     * Get the normalized role for routing purposes
     */
    public function getNormalizedRole(): string
    {
        return $this->normalizeRole($this->role);
    }

    // BFACES Helper Methods
    public function isHeadOfFamily(): bool
    {
        return $this->family_role === 'head_of_family';
    }

    public function isFamilyMember(): bool
    {
        return $this->family_role === 'family_member';
    }

    public function hasBfacesControlCode(): bool
    {
        return !empty($this->bfaces_control_code);
    }

    public function canAccessBfacesForm(): bool
    {
        return $this->isHeadOfFamily() &&
               $this->bfaces_status === 'form_accessible' &&
               $this->document_verification_status === 'approved';
    }

    public function isBfacesApproved(): bool
    {
        return $this->bfaces_status === 'approved';
    }

    public function generateBfacesControlCode(): string
    {
        do {
            $code = 'BFC' . str_pad(random_int(0, *********), 9, '0', STR_PAD_LEFT);
        } while (User::where('bfaces_control_code', $code)->exists());

        $this->update(['bfaces_control_code' => $code]);
        return $code;
    }

    public function getFamilyMembersCount(): int
    {
        if (!$this->isHeadOfFamily()) {
            return 0;
        }
        return $this->familyMembers()->count();
    }

    public function getDocumentVerificationProgress(): array
    {
        $totalRequired = $this->bfacesDocuments()->where('is_required', true)->count();
        $approved = $this->bfacesDocuments()->where('verification_status', 'approved')->count();

        return [
            'total' => $totalRequired,
            'approved' => $approved,
            'percentage' => $totalRequired > 0 ? round(($approved / $totalRequired) * 100) : 0
        ];
    }
}
