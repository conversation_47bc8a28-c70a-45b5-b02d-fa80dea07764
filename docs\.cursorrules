# Balagtas SocialCare: Project Intelligence

## Code Style Patterns

### Component Structure
- Components should be structured using the following pattern:
  - Each component in its own folder under a feature directory
  - index.js file for export
  - ComponentName.jsx for the main component
  - ComponentName.styles.js for styled components (if applicable)

### Naming Conventions
- PascalCase for component files and component names
- camelCase for helper functions and variables
- Use descriptive, semantic names for components
- Prefix handlers with "handle" (e.g., handleSubmit)
- Use plurals for collections/arrays

### Architecture Patterns
- Follow container/presentational pattern:
  - Container components handle data and business logic
  - Presentational components focus on UI rendering
- Keep components focused on a single responsibility
- Use custom hooks for reusable logic
- Store global state in React Context

### Project-Specific Patterns
- User roles determine access to features and components
- Service workflows follow a defined state machine pattern
- Form validation follows standardized error handling approach
- Application status uses predefined status constants

## File Organization 

### Frontend Structure
```
frontend/
├── src/
│   ├── assets/               # Static assets (images, fonts)
│   ├── components/           # Shared UI components
│   ├── contexts/             # React contexts for state management
│   ├── features/             # Feature-specific components
│   │   ├── auth/             # Authentication components
│   │   ├── client/           # Client role components
│   │   ├── admin/            # Admin role components
│   │   └── superadmin/       # Super admin role components
│   ├── layouts/              # Page layout components
│   ├── pages/                # Route-level page components
│   ├── services/             # API service layer
│   ├── utils/                # Helper functions and utilities
│   └── App.jsx               # Main app component
```

## Workflow Patterns

### Development Workflow
1. Component development with mock data
2. Integration with context/state
3. Unit testing
4. Integration with API services

### Feature Implementation
1. UI components implementation
2. Form validation logic
3. State management integration
4. API service integration
5. Error handling

## User Preferences

### UI/UX Preferences
- Clean, minimalist design
- Clear status indicators for application states
- Step-based workflows for complex processes
- Accessible forms with clear validation messages
- Mobile-responsive but desktop-optimized

### Implementation Preferences
- Prefer function components with hooks over class components
- Use destructuring for props
- Implement proper prop type validation
- Keep components reasonably sized (extract as needed)
- Implement proper error boundaries

## Critical Paths

### Key Implementation Areas
- Authentication and role-based access control
- Document upload and verification workflow
- Application state management across multiple steps
- Interview scheduling and management
- Service cooldown period enforcement

## Evolution of Decisions

### State Management
- Initially using React Context API
- May transition to Redux if state complexity increases
- Local component state for UI-specific state

### API Integration
- Mock services during initial development
- RESTful API integration when backend is available
- Token-based authentication with refresh mechanism

## Tool Usage

### Code Organization
- ESLint for code quality
- Prettier for formatting
- Git for version control
- Component-first development approach

### Libraries
- Material-UI for UI components
- Axios for API requests
- React Router for navigation
- React Context for state management

## Documentation Requirements

### Code Documentation
- JSDoc comments for functions and components
- Clear prop documentation
- Comments for complex logic
- README updates for new features

### Commit Style
- Semantic commit messages
- Feature-based branches
- Pull request for feature integration 