<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bfaces_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Document type and category
            $table->enum('document_type', [
                'proof_of_residency', 'utility_bill', 'barangay_clearance',
                'birth_certificate', 'marriage_certificate', 'death_certificate',
                'valid_id', 'income_certificate', 'medical_certificate',
                'school_enrollment', 'other'
            ]);
            $table->string('document_category')->default('verification'); // verification, relationship, income, etc.

            // File information
            $table->string('original_filename');
            $table->string('stored_filename');
            $table->string('file_path');
            $table->string('mime_type');
            $table->bigInteger('file_size'); // in bytes

            // Document verification
            $table->enum('verification_status', [
                'pending', 'under_review', 'approved', 'rejected', 'requires_resubmission'
            ])->default('pending');

            // Verification details
            $table->foreignId('reviewed_by')->nullable()->constrained('users');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable();

            // Document metadata
            $table->json('metadata')->nullable(); // Additional document-specific data
            $table->boolean('is_required')->default(true);
            $table->integer('submission_attempt')->default(1);

            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'document_type']);
            $table->index(['verification_status']);
            $table->index(['reviewed_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bfaces_documents');
    }
};
