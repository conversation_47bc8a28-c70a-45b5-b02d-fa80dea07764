<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // For development: Allow access if user has no role set
        if ($request->user()->role === null) {
            return $next($request);
        }



        if ($request->user()->hasRole($role)) {
            return $next($request);
        }

        // If user doesn't have the required role, redirect to their appropriate dashboard
        // But prevent redirect loops by checking if we're already on the target route
        $normalizedRole = $request->user()->getNormalizedRole();
        $currentPath = $request->path();

        switch ($normalizedRole) {
            case 'mswdo-officer':
                if ($currentPath !== 'mswdo-officer/dashboard') {
                    return redirect()->route('mswdo-officer.dashboard');
                }
                break;
            case 'social-worker':
                if ($currentPath !== 'social-worker/dashboard') {
                    return redirect()->route('social-worker.dashboard');
                }
                break;
            case 'applicant':
                if ($currentPath !== 'applicant/dashboard') {
                    return redirect()->route('applicant.dashboard');
                }
                break;
            default:
                return redirect()->route('login');
        }

        // If we reach here, it means we're on the correct dashboard but still don't have access
        // This should not happen, but let's allow access to prevent infinite loops
        return $next($request);
    }
} 