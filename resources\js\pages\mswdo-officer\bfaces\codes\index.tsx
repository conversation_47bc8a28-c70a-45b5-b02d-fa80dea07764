import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { type BreadcrumbItem } from '@/types';
import { 
    Search, Shield, Copy, Eye, Users, CheckCircle, 
    Calendar, MapPin, Phone, Mail, Download, Plus,
    QrCode, FileText, AlertTriangle
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'BFACES Management', href: '/mswdo-officer/bfaces' },
    { label: 'Control Codes', href: '/mswdo-officer/bfaces/codes' },
];

// Mock data for BFACES Control Codes
const bfacesCodes = [
    {
        id: 1,
        code: "BFC000123456",
        headOfFamily: "Maria Santos",
        email: "<EMAIL>",
        phone: "09123456789",
        barangay: "San Juan",
        familyMembers: 4,
        generatedAt: "2024-03-15",
        status: "active",
        formCompleted: true,
        lastUsed: "2024-03-20"
    },
    {
        id: 2,
        code: "BFC000789012",
        headOfFamily: "Juan Dela Cruz",
        email: "<EMAIL>",
        phone: "09187654321",
        barangay: "Wawa",
        familyMembers: 6,
        generatedAt: "2024-03-10",
        status: "active",
        formCompleted: true,
        lastUsed: "2024-03-19"
    },
    {
        id: 3,
        code: "BFC000345678",
        headOfFamily: "Ana Rodriguez",
        email: "<EMAIL>",
        phone: "09198765432",
        barangay: "Panginay",
        familyMembers: 3,
        generatedAt: "2024-03-08",
        status: "pending",
        formCompleted: false,
        lastUsed: null
    },
    {
        id: 4,
        code: "BFC000567890",
        headOfFamily: "Pedro Reyes",
        email: "<EMAIL>",
        phone: "09176543210",
        barangay: "Longos",
        familyMembers: 5,
        generatedAt: "2024-03-05",
        status: "expired",
        formCompleted: false,
        lastUsed: "2024-03-06"
    }
];

const getStatusBadge = (status: string) => {
    switch (status) {
        case 'active':
            return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
        case 'pending':
            return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
        case 'expired':
            return <Badge variant="destructive">Expired</Badge>;
        case 'revoked':
            return <Badge variant="outline" className="bg-gray-100 text-gray-800">Revoked</Badge>;
        default:
            return <Badge variant="outline">{status}</Badge>;
    }
};

const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
};

export default function BfacesControlCodes() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="BFACES Control Codes" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">
                            BFACES Control Codes
                        </h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">
                            Manage family registration control codes and access
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Download className="h-4 w-4 mr-2" />
                            Export Codes
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700">
                            <Plus className="h-4 w-4 mr-2" />
                            Generate New Code
                        </Button>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Active Codes</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <Shield className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">189</div>
                            <CardDescription className="text-green-600">
                                Currently in use
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-yellow-900">Pending Forms</CardTitle>
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <FileText className="h-5 w-5 text-yellow-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-yellow-900">58</div>
                            <CardDescription className="text-yellow-600">
                                Forms not completed
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Total Families</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Users className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">247</div>
                            <CardDescription className="text-blue-600">
                                Registered families
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-red-200 bg-gradient-to-br from-red-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-red-900">Expired/Revoked</CardTitle>
                            <div className="p-2 bg-red-100 rounded-lg">
                                <AlertTriangle className="h-5 w-5 text-red-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-red-900">12</div>
                            <CardDescription className="text-red-600">
                                Inactive codes
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Search */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search by control code, family name, or barangay..."
                            className="pl-10"
                        />
                    </div>
                </div>

                {/* Control Codes Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>BFACES Control Codes Registry</CardTitle>
                        <CardDescription>
                            Manage and monitor family registration control codes
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="font-bold">Control Code</TableHead>
                                        <TableHead className="font-bold">Head of Family</TableHead>
                                        <TableHead className="font-bold">Contact Info</TableHead>
                                        <TableHead className="font-bold">Barangay</TableHead>
                                        <TableHead className="text-center font-bold">Family Members</TableHead>
                                        <TableHead className="text-center font-bold">Status</TableHead>
                                        <TableHead className="text-center font-bold">Form Status</TableHead>
                                        <TableHead className="text-center font-bold">Generated</TableHead>
                                        <TableHead className="text-center font-bold">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {bfacesCodes.map((family) => (
                                        <TableRow key={family.id} className="hover:bg-purple-50/30">
                                            <TableCell className="font-mono font-medium">
                                                <div className="flex items-center gap-2">
                                                    <span className="text-purple-700">{family.code}</span>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => copyToClipboard(family.code)}
                                                        className="h-6 w-6 p-0"
                                                    >
                                                        <Copy className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <p className="font-medium">{family.headOfFamily}</p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p className="flex items-center gap-1">
                                                        <Mail className="h-3 w-3" />
                                                        {family.email}
                                                    </p>
                                                    <p className="flex items-center gap-1">
                                                        <Phone className="h-3 w-3" />
                                                        {family.phone}
                                                    </p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-1">
                                                    <MapPin className="h-3 w-3" />
                                                    {family.barangay}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                <Badge variant="outline">{family.familyMembers}</Badge>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {getStatusBadge(family.status)}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {family.formCompleted ? (
                                                    <Badge variant="default" className="bg-green-100 text-green-800">
                                                        <CheckCircle className="h-3 w-3 mr-1" />
                                                        Completed
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                                        Pending
                                                    </Badge>
                                                )}
                                            </TableCell>
                                            <TableCell className="text-center text-sm">
                                                <div className="flex items-center justify-center gap-1">
                                                    <Calendar className="h-3 w-3" />
                                                    {family.generatedAt}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                <div className="flex gap-1 justify-center">
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={`/mswdo-officer/bfaces/${family.id}`}>
                                                            <Eye className="h-3 w-3" />
                                                        </Link>
                                                    </Button>
                                                    <Button variant="outline" size="sm">
                                                        <QrCode className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
