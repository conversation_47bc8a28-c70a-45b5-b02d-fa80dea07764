<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BfacesDocument extends Model
{
    protected $fillable = [
        'user_id',
        'document_type',
        'document_category',
        'original_filename',
        'stored_filename',
        'file_path',
        'mime_type',
        'file_size',
        'verification_status',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'metadata',
        'is_required',
        'submission_attempt'
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'metadata' => 'array',
        'is_required' => 'boolean'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function isApproved(): bool
    {
        return $this->verification_status === 'approved';
    }

    public function isPending(): bool
    {
        return $this->verification_status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->verification_status === 'rejected';
    }

    public function getFileSizeFormatted(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
